# 应用访问地址，留空则系统自动获取，前端收银台会用到，建议设置，例如：https://token-pay.example.com
app_uri: 'https://payment.haiwailaba.cyou'
# 认证Token，对接会用到这个参数【很重要建议修改】
auth_token: '123234'
# 服务器HTTP监听地址
listen: ':8080'
# 静态资源路径，例如：/root/bepusdt/static；通过此参数可自定义模板，参考static目录，如非必要不建议修改。
static_path: ''
# SQLite数据库路径
sqlite_path: './data/sqlite.db'
# Tron网络GRPC节点，可选列表：https://developers.tron.network/docs/networks#public-node
tron_grpc_node: '18.141.79.38:50051'
# 日志输出路径
output_log: './logs/bepusdt.log'
# Webhook地址，留空则不启用
webhook_url: ''

pay:
  # usdt 支付原子颗粒度，0.01表示支付数额保留两位小数，相同金额时递增颗粒度为0.01，依次类推，如无特殊需求不建议修改。
  usdt_atom: 0.01
  usdc_atom: 0.01
  # USDT/USDC汇率，默认留空则获取Okx交易所的汇率(分钟/次，失败则取6.4)；支持多种写法，如：7.4表示固定7.4、～1.02表示最新汇率上浮2%、～0.97表示最新汇率下浮3%、+0.3表示最新加0.3、-0.2表示最新减0.2
  usdt_rate: '~0.98'
  usdc_rate: '~0.98'
  # 同上，TRX支付原子颗粒度
  trx_atom: 0.01
  # 同上，TRX汇率
  trx_rate: '~0.95'
  # 交易过期时间，单位秒，如无特殊需求不建议修改。
  expire_time: 1200
  # 启动时需要添加的钱包地址，多个请用半角符逗号,分开；当然，同样也支持通过机器人添加。
  wallet_address:
    # 写法举例： 币种:地址
    # - "tron.trx:TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
    # - "usdt.trc20:TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
    # - "usdt.bep20:******************************************"
  # 是否需要网络确认，禁用可以提高回调速度，启用则可以防止交易失败
  trade_is_confirmed: false
  # 支付监控的允许数额范围(闭区间)，设置合理数值可避免一些诱导式诈骗交易提醒
  payment_amount_min: 0.01
  payment_amount_max: 99999

evm_rpc:
  bsc: 'https://bsc-dataseed.bnbchain.org/'
  aptos: 'https://aptos-rest.publicnode.com/'
  xlayer: 'https://xlayerrpc.okx.com/'
  polygon: 'https://polygon-rpc.com/'
  arbitrum: 'https://arb1.arbitrum.io/rpc'
  ethereum: 'https://ethereum.publicnode.com/'
  base: 'https://base-public.nodies.app/'

bot:
  # Telegram Bot 管理员ID，必须设置，否则无法使用；群里 @BEpusdtChat 发送命令 /info 获取
  admin_id: 1222023646
  # Telegram 群组ID，设置之后机器人会将交易消息会推送到此群
  group_id: ''
  # Telegram Bot Token，必须设置，否则无法使用；通过 @BotFather 创建机器人获取
  token: '6955349926:AAFea_C13x5PeOJ0fqcDLUd8wfjayKAIEUI'
