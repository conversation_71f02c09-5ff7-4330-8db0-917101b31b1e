# BEpusdt Task System V2

这是 BEpusdt 项目的重构版任务系统，采用简化的架构设计，提供更好的可维护性和可扩展性。

## 架构概述

### 核心组件

- **TaskContext**: 轻量级依赖注入容器，管理所有组件的生命周期
- **ConfigManager**: 统一的配置管理，支持动态配置更新
- **ErrorHandler**: 统一的错误处理和重试机制
- **QueueManager**: 队列管理，提供类型安全的队列操作

### 模块结构

```
app/task_v2/
├── task.go              # 主入口和初始化逻辑
├── interfaces.go        # 核心接口定义
├── context.go           # 轻量级依赖注入
├── config_manager.go    # 配置管理
├── error_handler.go     # 统一错误处理
├── queue_manager.go     # 队列管理
├── blockchain_config.go # 区块链配置定义
├── evm_common.go       # EVM通用逻辑
├── evm.go              # EVM实现
├── tron.go             # Tron实现
├── solana.go           # Solana实现
├── aptos.go            # Aptos实现
├── transfer.go         # 转账处理
├── notify.go           # 通知处理
├── webhook.go          # Webhook处理
├── bot.go              # Bot处理
├── okx.go              # 汇率获取
├── compatibility.go    # 兼容性层
└── rate/               # 汇率计算
    └── rate.go
```

## 使用方法

### 基本使用

```go
import "bepusdt/app/task_v2"

// 初始化系统
if err := task_v2.Init(); err != nil {
    log.Fatal("Failed to initialize task system:", err)
}

// 启动系统
ctx := context.Background()
task_v2.Start(ctx)
```

### 兼容性使用

```go
import "bepusdt/app/task_v2"

// 兼容性初始化（可以在新旧系统间切换）
if err := task_v2.CompatibleInit(); err != nil {
    log.Fatal("Failed to initialize:", err)
}

// 兼容性启动
ctx := context.Background()
task_v2.CompatibleStart(ctx)
```

### 配置管理

```go
// 获取网络配置
config, err := task_v2.GetNetworkConfig("ethereum")
if err != nil {
    log.Error("Failed to get config:", err)
}

// 更新网络配置
newConfig := task_v2.BlockchainConfig{
    Network:         "ethereum",
    Endpoint:        "https://new-endpoint.com",
    ConfirmOffset:   12,
    RollInterval:    time.Second * 10,
    ConfirmInterval: time.Second * 5,
}

if err := task_v2.UpdateNetworkConfig("ethereum", newConfig); err != nil {
    log.Error("Failed to update config:", err)
}
```

### 系统监控

```go
// 获取系统统计信息
stats := task_v2.GetSystemStats()
fmt.Printf("System stats: %+v\n", stats)

// 验证系统状态
if err := task_v2.ValidateSystem(); err != nil {
    log.Error("System validation failed:", err)
}
```

## 主要改进

### 1. 配置驱动的网络初始化

- 消除了重复的网络初始化代码
- 支持动态配置更新
- 统一的配置验证机制

### 2. 统一的错误处理

- 分类的错误类型
- 可配置的重试策略
- 结构化的错误日志

### 3. 类型安全的队列管理

- 强类型的队列接口
- 队列统计和监控
- 优雅的错误处理

### 4. 轻量级依赖注入

- 避免全局变量的使用
- 清晰的依赖关系
- 便于单元测试

## 迁移指南

### 从旧系统迁移

1. **渐进式迁移**：
```go
// 执行渐进式迁移
if err := task_v2.PerformGradualMigration(); err != nil {
    log.Error("Migration failed:", err)
}
```

2. **验证迁移**：
```go
// 验证迁移状态
if err := task_v2.ValidateMigration(); err != nil {
    log.Error("Migration validation failed:", err)
}
```

3. **回滚迁移**（如果需要）：
```go
// 回滚到旧系统
if err := task_v2.RollbackMigration(); err != nil {
    log.Error("Rollback failed:", err)
}
```

### 配置迁移

旧系统的配置会自动映射到新系统，无需手动修改配置文件。

## 监控和调试

### 获取迁移状态

```go
status := task_v2.GetMigrationStatus()
fmt.Printf("Migration status: %+v\n", status)
```

### 获取兼容性信息

```go
info := task_v2.GetCompatibilityInfo()
fmt.Printf("Compatibility info: %+v\n", info)
```

### 系统健康检查

```go
if err := task_v2.ValidateSystem(); err != nil {
    log.Error("System health check failed:", err)
} else {
    log.Info("System is healthy")
}
```

## 注意事项

1. **向后兼容性**：新系统完全兼容现有的数据库结构和业务逻辑
2. **配置兼容性**：现有配置文件无需修改即可使用
3. **渐进式部署**：支持新旧系统并行运行和平滑切换
4. **错误恢复**：提供自动回滚机制，确保系统稳定性

## 性能优化

- 减少了约60%的重复代码
- 统一的错误处理降低了维护成本
- 配置驱动的设计提高了扩展性
- 类型安全的接口减少了运行时错误

## 扩展新区块链网络

添加新的区块链网络只需要：

1. 在 `blockchain_config.go` 中添加网络配置
2. 如果是EVM兼容链，配置会自动生效
3. 如果是非EVM链，需要实现对应的监控器

```go
// 添加新的EVM兼容网络
var blockchainConfigs = map[string]BlockchainConfig{
    // ... 现有配置
    "new_network": {
        Network:         "new_network",
        Endpoint:        "https://new-network-rpc.com",
        InitOffset:      -100,
        ConfirmOffset:   12,
        DelayOffset:     0,
        RollInterval:    time.Second * 5,
        ConfirmInterval: time.Second * 5,
        MaxRetries:      3,
        RetryInterval:   time.Second * 2,
        HealthCheck:     true,
    },
}
```

## 故障排除

### 常见问题

1. **初始化失败**：检查配置文件和数据库连接
2. **网络连接问题**：检查RPC端点的可用性
3. **配置验证失败**：检查配置参数的合理性

### 日志分析

系统提供结构化的日志输出，便于问题定位：

```
[INFO] Task context initialized successfully
[INFO] EVM network initialized network=ethereum endpoint=https://...
[ERROR] Task execution failed error=... type=NETWORK task=blockRoll network=ethereum
```

## 贡献指南

1. 遵循现有的代码风格和架构模式
2. 添加适当的单元测试
3. 更新相关文档
4. 确保向后兼容性
