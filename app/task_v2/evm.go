package task_v2

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"
	"sync"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/help"
	"bepusdt/app/model"

	"github.com/panjf2000/ants/v2"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
)

const (
	blockParseMaxNum = 10 // 每次解析区块的最大数量
	evmTransferEvent = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
)

var (
	chainBlockNum sync.Map // 存储各链的最新区块号
	client        = &http.Client{Timeout: time.Second * 30}
)

// 合约地址映射
var contractMap = map[string]string{
	strings.ToLower(conf.UsdtErc20):    model.OrderTradeTypeUsdtErc20,
	strings.ToLower(conf.UsdtBep20):    model.OrderTradeTypeUsdtBep20,
	strings.ToLower(conf.UsdtPolygon):  model.OrderTradeTypeUsdtPolygon,
	strings.ToLower(conf.UsdtArbitrum): model.OrderTradeTypeUsdtArbitrum,
	strings.ToLower(conf.UsdtXlayer):   model.OrderTradeTypeUsdtXlayer,
	strings.ToLower(conf.UsdcErc20):    model.OrderTradeTypeUsdcErc20,
	strings.ToLower(conf.UsdcBep20):    model.OrderTradeTypeUsdcBep20,
	strings.ToLower(conf.UsdcPolygon):  model.OrderTradeTypeUsdcPolygon,
	strings.ToLower(conf.UsdcArbitrum): model.OrderTradeTypeUsdcArbitrum,
	strings.ToLower(conf.UsdcXlayer):   model.OrderTradeTypeUsdcXlayer,
	strings.ToLower(conf.UsdcBase):     model.OrderTradeTypeUsdcBase,
}

// 小数位数映射
var decimals = map[string]int32{
	strings.ToLower(conf.UsdtErc20):    conf.UsdtEthDecimals,
	strings.ToLower(conf.UsdtBep20):    conf.UsdtBscDecimals,
	strings.ToLower(conf.UsdtPolygon):  conf.UsdtPolygonDecimals,
	strings.ToLower(conf.UsdtArbitrum): conf.UsdtArbitrumDecimals,
	strings.ToLower(conf.UsdtXlayer):   conf.UsdtXlayerDecimals,
	strings.ToLower(conf.UsdcErc20):    conf.UsdcEthDecimals,
	strings.ToLower(conf.UsdcBep20):    conf.UsdcBscDecimals,
	strings.ToLower(conf.UsdcPolygon):  conf.UsdcPolygonDecimals,
	strings.ToLower(conf.UsdcArbitrum): conf.UsdcArbitrumDecimals,
	strings.ToLower(conf.UsdcXlayer):   conf.UsdcXlayerDecimals,
	strings.ToLower(conf.UsdcBase):     conf.UsdcBaseDecimals,
}

// 网络代币映射
var networkTokenMap = map[string][]string{
	conf.Ethereum: {model.OrderTradeTypeUsdtErc20, model.OrderTradeTypeUsdcErc20},
	conf.Bsc:      {model.OrderTradeTypeUsdtBep20, model.OrderTradeTypeUsdcBep20},
	conf.Polygon:  {model.OrderTradeTypeUsdtPolygon, model.OrderTradeTypeUsdcPolygon},
	conf.Arbitrum: {model.OrderTradeTypeUsdtArbitrum, model.OrderTradeTypeUsdcArbitrum},
	conf.Xlayer:   {model.OrderTradeTypeUsdtXlayer, model.OrderTradeTypeUsdcXlayer},
	conf.Base:     {model.OrderTradeTypeUsdcBase}, // Base 只有 USDC
}

// blockRoll 区块轮询
func (em *evmMonitor) blockRoll(ctx context.Context) {
	if rollBreak(em.network) {
		return
	}

	post := []byte(`{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`)
	req, err := http.NewRequestWithContext(ctx, "POST", em.config.Endpoint, bytes.NewBuffer(post))
	if err != nil {
		em.reportError(ErrorTypeNetwork, "blockRoll", err, map[string]interface{}{
			"method": "eth_blockNumber",
		})
		return
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		if em.reportError(ErrorTypeNetwork, "blockRoll", err, nil) {
			em.tryFallbackEndpoint()
		}
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		em.reportError(ErrorTypeNetwork, "blockRoll", err, nil)
		return
	}

	var res = gjson.ParseBytes(body)
	var now = help.HexStr2Int(res.Get("result").String()).Int64() - em.config.DelayOffset
	if now <= 0 {
		return
	}

	if conf.GetTradeIsConfirmed() {
		now = now - em.config.ConfirmOffset
	}

	var lastBlockNumber int64
	if v, ok := chainBlockNum.Load(em.network); ok {
		lastBlockNumber = v.(int64)
	}

	if now-lastBlockNumber > conf.BlockHeightMaxDiff {
		lastBlockNumber = em.blockInitOffset(now, em.config.InitOffset) - 1
	}

	chainBlockNum.Store(em.network, now)
	if now <= lastBlockNumber {
		return
	}

	for from := lastBlockNumber + 1; from <= now; from += blockParseMaxNum {
		to := from + blockParseMaxNum - 1
		if to > now {
			to = now
		}

		em.blockScanQueue.In <- evmBlock{From: from, To: to}
	}
}

// blockInitOffset 区块初始化偏移
func (em *evmMonitor) blockInitOffset(now, offset int64) int64 {
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for b := now; b > now+offset; b -= blockParseMaxNum {
			if rollBreak(em.network) {
				return
			}

			em.blockScanQueue.In <- evmBlock{From: b - blockParseMaxNum + 1, To: b}
			<-ticker.C
		}
	}()

	return now
}

// blockDispatch 区块分发
func (em *evmMonitor) blockDispatch(ctx context.Context) {
	p, err := ants.NewPoolWithFunc(2, em.getBlockByNumber)
	if err != nil {
		panic(err)
	}
	defer p.Release()

	for {
		select {
		case <-ctx.Done():
			return
		case n := <-em.blockScanQueue.Out:
			if err := p.Invoke(n); err != nil {
				em.blockScanQueue.In <- n
				em.reportError(ErrorTypeQueue, "blockDispatch", err, map[string]interface{}{
					"block_range": fmt.Sprintf("%d-%d", n.From, n.To),
				})
			}
		}
	}
}

// getBlockByNumber 获取区块信息
func (em *evmMonitor) getBlockByNumber(n any) {
	b := n.(evmBlock)

	conf.SetBlockTotal(em.network)

	// 构建批量请求
	var requests []string
	for i := b.From; i <= b.To; i++ {
		request := fmt.Sprintf(`{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["0x%x",false],"id":%d}`, i, i)
		requests = append(requests, request)
	}

	batchRequest := "[" + strings.Join(requests, ",") + "]"

	req, err := http.NewRequest("POST", em.config.Endpoint, strings.NewReader(batchRequest))
	if err != nil {
		conf.SetBlockFail(em.network)
		em.blockScanQueue.In <- b
		em.reportError(ErrorTypeNetwork, "getBlockByNumber", err, nil)
		return
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		conf.SetBlockFail(em.network)
		em.blockScanQueue.In <- b
		em.reportError(ErrorTypeNetwork, "getBlockByNumber", err, nil)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		conf.SetBlockFail(em.network)
		em.blockScanQueue.In <- b
		em.reportError(ErrorTypeNetwork, "getBlockByNumber", err, nil)
		return
	}

	timestamp := make(map[string]time.Time)
	for _, itm := range gjson.ParseBytes(body).Array() {
		if itm.Get("error").Exists() {
			conf.SetBlockFail(em.network)
			em.blockScanQueue.In <- b
			em.reportError(ErrorTypeRPC, "getBlockByNumber",
				fmt.Errorf("RPC error: %s", itm.Get("error").String()), nil)
			return
		}

		timestamp[itm.Get("result.number").String()] = time.Unix(help.HexStr2Int(itm.Get("result.timestamp").String()).Int64(), 0)
	}

	transfers, err := em.parseBlockTransfer(b, timestamp)
	if err != nil {
		conf.SetBlockFail(em.network)
		em.blockScanQueue.In <- b
		em.reportError(ErrorTypeParsing, "parseBlockTransfer", err, nil)
		return
	}

	if len(transfers) > 0 {
		if err := em.ctx.SendToTransferQueue(transfers); err != nil {
			em.reportError(ErrorTypeQueue, "sendToTransferQueue", err, map[string]interface{}{
				"transfer_count": len(transfers),
			})
		}
	}

	em.reportSuccess("getBlockByNumber", map[string]interface{}{
		"block_range":    fmt.Sprintf("%d-%d", b.From, b.To),
		"transfer_count": len(transfers),
		"success_rate":   conf.GetBlockSuccRate(em.network),
	})
}

// parseBlockTransfer 解析区块转账信息
func (em *evmMonitor) parseBlockTransfer(b evmBlock, timestamp map[string]time.Time) ([]Transfer, error) {
	var transfers []Transfer

	// 构建批量日志请求
	var requests []string
	for i := b.From; i <= b.To; i++ {
		request := fmt.Sprintf(`{"jsonrpc":"2.0","method":"eth_getLogs","params":[{"fromBlock":"0x%x","toBlock":"0x%x","topics":["%s"]}],"id":%d}`,
			i, i, evmTransferEvent, i)
		requests = append(requests, request)
	}

	batchRequest := "[" + strings.Join(requests, ",") + "]"

	req, err := http.NewRequest("POST", em.config.Endpoint, strings.NewReader(batchRequest))
	if err != nil {
		return nil, fmt.Errorf("failed to create logs request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get logs: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read logs response: %w", err)
	}

	// 解析日志
	for _, itm := range gjson.ParseBytes(body).Array() {
		if itm.Get("error").Exists() {
			return nil, fmt.Errorf("RPC error in logs: %s", itm.Get("error").String())
		}

		for _, log := range itm.Get("result").Array() {
			transfer, err := em.parseTransferLog(log, timestamp)
			if err != nil {
				continue // 跳过解析失败的日志
			}
			if transfer != nil {
				transfers = append(transfers, *transfer)
			}
		}
	}

	return transfers, nil
}

// parseTransferLog 解析单个转账日志
func (em *evmMonitor) parseTransferLog(log gjson.Result, timestamp map[string]time.Time) (*Transfer, error) {
	contractAddress := strings.ToLower(log.Get("address").String())
	tradeType, exists := contractMap[contractAddress]
	if !exists {
		return nil, nil // 不是我们关心的合约
	}

	topics := log.Get("topics").Array()
	if len(topics) < 3 {
		return nil, fmt.Errorf("invalid transfer log topics")
	}

	// 解析地址
	fromAddress := "0x" + topics[1].String()[26:]
	toAddress := "0x" + topics[2].String()[26:]

	// 解析金额
	data := log.Get("data").String()
	if len(data) < 2 {
		return nil, fmt.Errorf("invalid transfer log data")
	}

	amountHex := data[2:] // 去掉0x前缀
	amountBig := new(big.Int)
	amountBig.SetString(amountHex, 16)

	// 获取小数位数
	decimalPlaces, exists := decimals[contractAddress]
	if !exists {
		return nil, fmt.Errorf("decimal not found for contract: %s", contractAddress)
	}

	// 转换金额
	divisor := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimalPlaces)), nil)
	amount := decimal.NewFromBigInt(amountBig, 0).Div(decimal.NewFromBigInt(divisor, 0))

	// 检查金额范围
	if !inAmountRange(amount) {
		return nil, nil // 金额不在范围内
	}

	// 获取时间戳
	blockNumber := log.Get("blockNumber").String()
	blockTime, exists := timestamp[blockNumber]
	if !exists {
		blockTime = time.Now()
	}

	blockNum := help.HexStr2Int(blockNumber).Int64()

	return &Transfer{
		Network:     em.network,
		TxHash:      log.Get("transactionHash").String(),
		Amount:      amount,
		FromAddress: fromAddress,
		RecvAddress: toAddress,
		Timestamp:   blockTime,
		TradeType:   tradeType,
		BlockNum:    blockNum,
	}, nil
}

// rollBreak 检查是否应该暂停区块扫描
func rollBreak(network string) bool {
	token, ok := networkTokenMap[network]
	if !ok {
		return true
	}

	var count int64 = 0
	model.DB.Model(&model.TradeOrders{}).Where("status = ? and trade_type in (?)", model.OrderStatusWaiting, token).Count(&count)
	if count > 0 {
		return false
	}

	model.DB.Model(&model.WalletAddress{}).Where("other_notify = ? and trade_type in (?)", model.OtherNotifyEnable, token).Count(&count)
	return count == 0
}

// tradeConfirmHandle EVM交易确认处理
func (em *evmMonitor) tradeConfirmHandle(ctx context.Context) {
	// 获取确认中的订单
	var orders []model.TradeOrders
	model.DB.Where("status = ? AND trade_type IN (?)",
		model.OrderStatusConfirming, networkTokenMap[em.network]).Find(&orders)

	if len(orders) == 0 {
		return
	}

	em.ctx.LogInfo("Processing confirming orders", "network", em.network, "count", len(orders))

	for _, order := range orders {
		if err := em.processConfirmingOrder(&order); err != nil {
			em.reportError(ErrorTypeRPC, "tradeConfirmHandle", err, map[string]interface{}{
				"order_id":   order.OrderId,
				"trade_hash": order.TradeHash,
			})
		}
	}
}

// processConfirmingOrder 处理确认中的订单
func (em *evmMonitor) processConfirmingOrder(order *model.TradeOrders) error {
	// 获取交易回执
	receipt, err := em.getTransactionReceipt(order.TradeHash)
	if err != nil {
		return fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	if receipt == nil {
		// 交易还未上链，继续等待
		return nil
	}

	// 检查交易状态
	if !em.isTransactionSuccess(receipt) {
		// 交易失败
		order.SetFailed()
		em.ctx.LogWarn("Transaction failed", "order_id", order.OrderId, "trade_hash", order.TradeHash)
		return nil
	}

	// 检查确认数
	currentBlock, err := em.getCurrentBlockNumber()
	if err != nil {
		return fmt.Errorf("failed to get current block number: %w", err)
	}

	txBlock := em.getTransactionBlockNumber(receipt)
	confirmations := currentBlock - txBlock

	if confirmations >= em.config.ConfirmOffset {
		// 确认数足够，标记为成功
		order.SetSuccess()
		em.ctx.LogInfo("Order confirmed successfully",
			"order_id", order.OrderId,
			"confirmations", confirmations)

		// 处理成功订单的后续逻辑
		if err := processSuccessfulOrder(em.ctx, order); err != nil {
			em.ctx.LogError("Failed to process successful order",
				"order_id", order.OrderId, "error", err.Error())
		}
	}

	return nil
}

// getTransactionReceipt 获取交易回执
func (em *evmMonitor) getTransactionReceipt(txHash string) (interface{}, error) {
	post := fmt.Sprintf(`{"jsonrpc":"2.0","method":"eth_getTransactionReceipt","params":["%s"],"id":1}`, txHash)

	req, err := http.NewRequest("POST", em.config.Endpoint, strings.NewReader(post))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	result := gjson.ParseBytes(body)
	if result.Get("error").Exists() {
		return nil, fmt.Errorf("RPC error: %s", result.Get("error").String())
	}

	receiptData := result.Get("result")
	if !receiptData.Exists() || receiptData.String() == "null" {
		return nil, nil // 交易还未上链
	}

	return receiptData.Value(), nil
}

// isTransactionSuccess 检查交易是否成功
func (em *evmMonitor) isTransactionSuccess(receipt interface{}) bool {
	if receipt == nil {
		return false
	}

	// 将interface{}转换为gjson.Result进行解析
	receiptJSON := gjson.Parse(fmt.Sprintf("%v", receipt))
	status := receiptJSON.Get("status").String()

	// status为"0x1"表示成功，"0x0"表示失败
	return status == "0x1"
}

// getCurrentBlockNumber 获取当前区块号
func (em *evmMonitor) getCurrentBlockNumber() (int64, error) {
	post := `{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`

	req, err := http.NewRequest("POST", em.config.Endpoint, strings.NewReader(post))
	if err != nil {
		return 0, err
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	result := gjson.ParseBytes(body)
	if result.Get("error").Exists() {
		return 0, fmt.Errorf("RPC error: %s", result.Get("error").String())
	}

	blockNumberHex := result.Get("result").String()
	return help.HexStr2Int(blockNumberHex).Int64(), nil
}

// getTransactionBlockNumber 从交易回执中获取区块号
func (em *evmMonitor) getTransactionBlockNumber(receipt interface{}) int64 {
	if receipt == nil {
		return 0
	}

	receiptJSON := gjson.Parse(fmt.Sprintf("%v", receipt))
	blockNumberHex := receiptJSON.Get("blockNumber").String()
	return help.HexStr2Int(blockNumberHex).Int64()
}
