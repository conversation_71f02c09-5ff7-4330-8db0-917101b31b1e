package task_v2

import (
	"fmt"

	"bepusdt/app/log"
)

// TaskContext 任务上下文，轻量级依赖注入容器
type TaskContext struct {
	configProvider ConfigProvider
	errorReporter  ErrorReporter
	queueProvider  QueueProvider
	logger         Logger
}

// loggerAdapter 日志适配器，将现有日志系统适配到接口
type loggerAdapter struct{}

func (la *loggerAdapter) Info(msg string, args ...interface{}) {
	log.Info(fmt.Sprintf(msg, args...))
}

func (la *loggerAdapter) Warn(msg string, args ...interface{}) {
	log.Warn(fmt.Sprintf(msg, args...))
}

func (la *loggerAdapter) Error(msg string, args ...interface{}) {
	log.Error(fmt.Sprintf(msg, args...))
}

var globalContext *TaskContext

// InitTaskContext 初始化任务上下文
func InitTaskContext() error {
	// 创建各个组件
	configMgr := NewConfigManager()
	errorHandler := NewErrorHandler()
	queueMgr := NewQueueManager()
	logger := &loggerAdapter{}

	// 验证所有配置
	if err := configMgr.ValidateAllConfigs(); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	globalContext = &TaskContext{
		configProvider: configMgr,
		errorReporter:  errorHandler,
		queueProvider:  queueMgr,
		logger:         logger,
	}

	log.Info("Task context initialized successfully")
	return nil
}

// GetTaskContext 获取全局任务上下文
func GetTaskContext() *TaskContext {
	return globalContext
}

// GetConfigProvider 获取配置提供者
func (tc *TaskContext) GetConfigProvider() ConfigProvider {
	return tc.configProvider
}

// GetErrorReporter 获取错误报告者
func (tc *TaskContext) GetErrorReporter() ErrorReporter {
	return tc.errorReporter
}

// GetQueueProvider 获取队列提供者
func (tc *TaskContext) GetQueueProvider() QueueProvider {
	return tc.queueProvider
}

// GetLogger 获取日志记录器
func (tc *TaskContext) GetLogger() Logger {
	return tc.logger
}

// ReportError 便捷的错误报告方法
func (tc *TaskContext) ReportError(errorType ErrorType, taskName, network string, err error, context map[string]interface{}) bool {
	return tc.errorReporter.ReportError(errorType, taskName, network, err, context)
}

// ReportSuccess 便捷的成功报告方法
func (tc *TaskContext) ReportSuccess(taskName, network string, context map[string]interface{}) {
	if eh, ok := tc.errorReporter.(*ErrorHandler); ok {
		eh.ReportSuccess(taskName, network, context)
	}
}

// GetConfig 便捷的配置获取方法
func (tc *TaskContext) GetConfig(network string) (BlockchainConfig, bool) {
	return tc.configProvider.GetConfig(network)
}

// UpdateConfig 便捷的配置更新方法
func (tc *TaskContext) UpdateConfig(network string, config BlockchainConfig) error {
	return tc.configProvider.UpdateConfig(network, config)
}

// SendToTransferQueue 便捷的转账队列发送方法
func (tc *TaskContext) SendToTransferQueue(transfers []Transfer) error {
	return tc.queueProvider.GetTransferQueue().Send(transfers)
}

// SendToResourceQueue 便捷的资源队列发送方法
func (tc *TaskContext) SendToResourceQueue(resources []Resource) error {
	return tc.queueProvider.GetResourceQueue().Send(resources)
}

// SendToNotOrderQueue 便捷的非订单队列发送方法
func (tc *TaskContext) SendToNotOrderQueue(transfers []Transfer) error {
	return tc.queueProvider.GetNotOrderQueue().Send(transfers)
}

// GetQueueStats 便捷的队列统计获取方法
func (tc *TaskContext) GetQueueStats(queueName string) *QueueStats {
	return tc.queueProvider.GetStats(queueName)
}

// LogInfo 便捷的信息日志方法
func (tc *TaskContext) LogInfo(msg string, args ...interface{}) {
	tc.logger.Info(msg, args...)
}

// LogWarn 便捷的警告日志方法
func (tc *TaskContext) LogWarn(msg string, args ...interface{}) {
	tc.logger.Warn(msg, args...)
}

// LogError 便捷的错误日志方法
func (tc *TaskContext) LogError(msg string, args ...interface{}) {
	tc.logger.Error(msg, args...)
}

// Validate 验证上下文是否正确初始化
func (tc *TaskContext) Validate() error {
	if tc.configProvider == nil {
		return fmt.Errorf("config provider is nil")
	}
	if tc.errorReporter == nil {
		return fmt.Errorf("error reporter is nil")
	}
	if tc.queueProvider == nil {
		return fmt.Errorf("queue provider is nil")
	}
	if tc.logger == nil {
		return fmt.Errorf("logger is nil")
	}
	return nil
}

// Close 关闭任务上下文
func (tc *TaskContext) Close() error {
	if qm, ok := tc.queueProvider.(*QueueManager); ok {
		if err := qm.Close(); err != nil {
			return fmt.Errorf("failed to close queue manager: %w", err)
		}
	}

	tc.logger.Info("Task context closed")
	return nil
}

// IsInitialized 检查上下文是否已初始化
func IsInitialized() bool {
	return globalContext != nil
}

// MustGetTaskContext 获取任务上下文，如果未初始化则panic
func MustGetTaskContext() *TaskContext {
	if globalContext == nil {
		panic("task context not initialized, call InitTaskContext() first")
	}
	return globalContext
}
