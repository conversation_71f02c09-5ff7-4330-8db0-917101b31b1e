package task_v2

import (
	"context"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

// ConfigProvider 配置提供者接口
type ConfigProvider interface {
	GetConfig(network string) (BlockchainConfig, bool)
	UpdateConfig(network string, config BlockchainConfig) error
	RegisterValidator(name string, validator ConfigValidator)
	RegisterChangeListener(listener ConfigChangeListener)
}

// ErrorReporter 错误报告者接口
type ErrorReporter interface {
	ReportError(errorType ErrorType, taskName, network string, err error, context map[string]interface{}) bool
	GetMaxRetries(errorType ErrorType) int
	GetRetryDelay(errorType ErrorType) time.Duration
}

// QueueProvider 队列提供者接口
type QueueProvider interface {
	GetTransferQueue() TransferQueue
	GetResourceQueue() ResourceQueue
	GetNotOrderQueue() TransferQueue
	GetStats(queueName string) *QueueStats
	UpdateStats(queueName string, processed bool)
}

// Logger 日志接口
type Logger interface {
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
}

// TransferQueue 转账队列接口
type TransferQueue interface {
	Send(transfers []Transfer) error
	Receive() <-chan []Transfer
}

// ResourceQueue 资源队列接口
type ResourceQueue interface {
	Send(resources []Resource) error
	Receive() <-chan []Resource
}

// BlockchainMonitor 区块链监控器接口
type BlockchainMonitor interface {
	Start(ctx context.Context) error
	Stop() error
	GetNetwork() string
	UpdateConfig(config BlockchainConfig) error
}

// ConfigValidator 配置验证器类型
type ConfigValidator func(config BlockchainConfig) error

// ConfigChangeListener 配置变更监听器类型
type ConfigChangeListener func(network string, oldConfig, newConfig BlockchainConfig)

// ErrorType 错误类型枚举
type ErrorType int

const (
	ErrorTypeNetwork ErrorType = iota
	ErrorTypeRPC
	ErrorTypeDatabase
	ErrorTypeParsing
	ErrorTypeQueue
	ErrorTypeConfiguration
)

// BlockchainConfig 区块链配置结构
type BlockchainConfig struct {
	Network          string
	Endpoint         string
	InitOffset       int64
	ConfirmOffset    int64
	DelayOffset      int64
	RollInterval     time.Duration
	ConfirmInterval  time.Duration
	MaxRetries       int
	RetryInterval    time.Duration
	HealthCheck      bool
	FallbackEndpoint string
}

// Transfer 转账数据结构
type Transfer struct {
	Network     string
	TxHash      string
	Amount      decimal.Decimal
	FromAddress string
	RecvAddress string
	Timestamp   time.Time
	TradeType   string
	BlockNum    int64
}

// Resource 资源数据结构
type Resource struct {
	ID           string
	Type         int32
	Balance      int64
	FromAddress  string
	RecvAddress  string
	Timestamp    time.Time
	ResourceCode int32
}

// QueueStats 队列统计信息
type QueueStats struct {
	TotalMessages   int64
	ProcessedCount  int64
	ErrorCount      int64
	LastProcessTime time.Time
}

// TaskError 任务错误结构
type TaskError struct {
	Type     ErrorType
	TaskName string
	Network  string
	Err      error
	Retries  int
	Context  map[string]interface{}
}

func (te TaskError) Error() string {
	return te.Err.Error()
}

// Validate 验证区块链配置
func (bc BlockchainConfig) Validate() error {
	if bc.Network == "" {
		return ErrEmptyNetwork
	}
	if bc.Endpoint == "" {
		return ErrEmptyEndpoint
	}
	if bc.ConfirmOffset < 0 {
		return ErrInvalidConfirmOffset
	}
	if bc.RollInterval <= 0 {
		return ErrInvalidRollInterval
	}
	return nil
}

// 预定义错误
var (
	ErrEmptyNetwork         = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("network cannot be empty")}
	ErrEmptyEndpoint        = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("endpoint cannot be empty")}
	ErrInvalidConfirmOffset = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("confirm offset must be non-negative")}
	ErrInvalidRollInterval  = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("roll interval must be positive")}
	ErrConfigNotFound       = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("config not found")}
	ErrValidationFailed     = &TaskError{Type: ErrorTypeConfiguration, Err: fmt.Errorf("validation failed")}
)
