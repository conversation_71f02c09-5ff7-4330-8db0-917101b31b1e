package rate

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"bepusdt/app/conf"

	"github.com/shopspring/decimal"
)

var (
	// 原始汇率存储
	okxUsdtRawRate float64 = conf.DefaultUsdtCnyRate
	okxUsdcRawRate float64 = conf.DefaultUsdcCnyRate
	okxTrxRawRate  float64 = conf.DefaultTrxCnyRate

	// 互斥锁保护汇率数据
	rateMutex sync.RWMutex
)

// GetOkxUsdtRawRate 获取OKX USDT原始汇率
func GetOkxUsdtRawRate() float64 {
	rateMutex.RLock()
	defer rateMutex.RUnlock()
	return okxUsdtRawRate
}

// GetOkxUsdcRawRate 获取OKX USDC原始汇率
func GetOkxUsdcRawRate() float64 {
	rateMutex.RLock()
	defer rateMutex.RUnlock()
	return okxUsdcRawRate
}

// GetOkxTrxRawRate 获取OKX TRX原始汇率
func GetOkxTrxRawRate() float64 {
	rateMutex.RLock()
	defer rateMutex.RUnlock()
	return okxTrxRawRate
}

// SetOkxUsdtRawRate 设置OKX USDT原始汇率
func SetOkxUsdtRawRate(rate float64) error {
	if rate <= 0 {
		return fmt.Errorf("invalid USDT rate: %f", rate)
	}
	
	rateMutex.Lock()
	defer rateMutex.Unlock()
	okxUsdtRawRate = rate
	return nil
}

// SetOkxUsdcRawRate 设置OKX USDC原始汇率
func SetOkxUsdcRawRate(rate float64) error {
	if rate <= 0 {
		return fmt.Errorf("invalid USDC rate: %f", rate)
	}
	
	rateMutex.Lock()
	defer rateMutex.Unlock()
	okxUsdcRawRate = rate
	return nil
}

// SetOkxTrxRawRate 设置OKX TRX原始汇率
func SetOkxTrxRawRate(rate float64) error {
	if rate <= 0 {
		return fmt.Errorf("invalid TRX rate: %f", rate)
	}
	
	rateMutex.Lock()
	defer rateMutex.Unlock()
	okxTrxRawRate = rate
	return nil
}

// GetUsdtCalcRate 获取USDT计算汇率
func GetUsdtCalcRate() float64 {
	configRate := conf.GetUsdtRate()
	if configRate == "" {
		return GetOkxUsdtRawRate()
	}
	return ParseFloatRate(configRate, GetOkxUsdtRawRate())
}

// GetUsdcCalcRate 获取USDC计算汇率
func GetUsdcCalcRate() float64 {
	configRate := conf.GetUsdcRate()
	if configRate == "" {
		return GetOkxUsdcRawRate()
	}
	return ParseFloatRate(configRate, GetOkxUsdcRawRate())
}

// GetTrxCalcRate 获取TRX计算汇率
func GetTrxCalcRate() float64 {
	configRate := conf.GetTrxRate()
	if configRate == "" {
		return GetOkxTrxRawRate()
	}
	return ParseFloatRate(configRate, GetOkxTrxRawRate())
}

// ParseFloatRate 解析浮点汇率
// 支持多种格式：
// - 固定值：7.4
// - 百分比调整：~1.02 (上浮2%), ~0.97 (下浮3%)
// - 固定调整：+0.3 (加0.3), -0.2 (减0.2)
func ParseFloatRate(rateStr string, baseRate float64) float64 {
	rateStr = strings.TrimSpace(rateStr)
	if rateStr == "" {
		return baseRate
	}

	// 处理百分比调整 ~1.02, ~0.97
	if strings.HasPrefix(rateStr, "~") {
		multiplierStr := strings.TrimPrefix(rateStr, "~")
		if multiplier, err := strconv.ParseFloat(multiplierStr, 64); err == nil {
			return baseRate * multiplier
		}
		return baseRate
	}

	// 处理固定调整 +0.3, -0.2
	if after, ok :=strings.CutPrefix(rateStr, "+"); ok  {
		if adjust, err := strconv.ParseFloat(after, 64); err == nil {
			return baseRate + adjust
		}
		return baseRate
	}

	if after, ok :=strings.CutPrefix(rateStr, "-"); ok  {
		if adjust, err := strconv.ParseFloat(after, 64); err == nil {
			return baseRate - adjust
		}
		return baseRate
	}

	// 处理固定值
	if rate, err := strconv.ParseFloat(rateStr, 64); err == nil {
		return rate
	}

	// 解析失败，返回基础汇率
	return baseRate
}

// CalculateAmount 根据汇率计算金额
func CalculateAmount(cnyAmount float64, rate float64) decimal.Decimal {
	if rate <= 0 {
		return decimal.Zero
	}
	
	cnyDecimal := decimal.NewFromFloat(cnyAmount)
	rateDecimal := decimal.NewFromFloat(rate)
	
	return cnyDecimal.Div(rateDecimal)
}

// CalculateCNYAmount 根据代币数量计算人民币金额
func CalculateCNYAmount(tokenAmount decimal.Decimal, rate float64) decimal.Decimal {
	if rate <= 0 {
		return decimal.Zero
	}
	
	rateDecimal := decimal.NewFromFloat(rate)
	return tokenAmount.Mul(rateDecimal)
}

// ValidateRate 验证汇率是否合理
func ValidateRate(rate float64, tokenType string) error {
	if rate <= 0 {
		return fmt.Errorf("rate must be positive, got: %f", rate)
	}

	// 设置合理的汇率范围
	var minRate, maxRate float64
	switch strings.ToUpper(tokenType) {
	case "USDT", "USDC":
		minRate, maxRate = 5.0, 10.0 // 稳定币合理范围
	case "TRX":
		minRate, maxRate = 0.1, 2.0 // TRX合理范围
	default:
		return nil // 未知代币类型，跳过验证
	}

	if rate < minRate || rate > maxRate {
		return fmt.Errorf("rate %f for %s is out of reasonable range [%f, %f]", 
			rate, tokenType, minRate, maxRate)
	}

	return nil
}

// GetRateInfo 获取汇率信息
func GetRateInfo() map[string]interface{} {
	rateMutex.RLock()
	defer rateMutex.RUnlock()

	return map[string]interface{}{
		"usdt": map[string]interface{}{
			"raw_rate":  okxUsdtRawRate,
			"calc_rate": GetUsdtCalcRate(),
			"config":    conf.GetUsdtRate(),
		},
		"usdc": map[string]interface{}{
			"raw_rate":  okxUsdcRawRate,
			"calc_rate": GetUsdcCalcRate(),
			"config":    conf.GetUsdcRate(),
		},
		"trx": map[string]interface{}{
			"raw_rate":  okxTrxRawRate,
			"calc_rate": GetTrxCalcRate(),
			"config":    conf.GetTrxRate(),
		},
	}
}

// FormatRate 格式化汇率显示
func FormatRate(rate float64) string {
	return fmt.Sprintf("%.4f", rate)
}

// CompareRates 比较两个汇率的差异
func CompareRates(rate1, rate2 float64) float64 {
	if rate2 == 0 {
		return 0
	}
	return (rate1 - rate2) / rate2
}

// IsRateStable 检查汇率是否稳定（变化小于阈值）
func IsRateStable(oldRate, newRate, threshold float64) bool {
	change := CompareRates(newRate, oldRate)
	return change >= -threshold && change <= threshold
}

// CalculateRateChange 计算汇率变化百分比
func CalculateRateChange(oldRate, newRate float64) float64 {
	if oldRate == 0 {
		return 0
	}
	return ((newRate - oldRate) / oldRate) * 100
}

// GetRateByTokenType 根据代币类型获取汇率
func GetRateByTokenType(tokenType string) float64 {
	switch strings.ToUpper(tokenType) {
	case "USDT":
		return GetUsdtCalcRate()
	case "USDC":
		return GetUsdcCalcRate()
	case "TRX":
		return GetTrxCalcRate()
	default:
		return 0
	}
}

// SetRateByTokenType 根据代币类型设置汇率
func SetRateByTokenType(tokenType string, rate float64) error {
	switch strings.ToUpper(tokenType) {
	case "USDT":
		return SetOkxUsdtRawRate(rate)
	case "USDC":
		return SetOkxUsdcRawRate(rate)
	case "TRX":
		return SetOkxTrxRawRate(rate)
	default:
		return fmt.Errorf("unsupported token type: %s", tokenType)
	}
}

// ResetRatesToDefault 重置汇率为默认值
func ResetRatesToDefault() {
	rateMutex.Lock()
	defer rateMutex.Unlock()
	
	okxUsdtRawRate = conf.DefaultUsdtCnyRate
	okxUsdcRawRate = conf.DefaultUsdcCnyRate
	okxTrxRawRate = conf.DefaultTrxCnyRate
}

// GetRateStatistics 获取汇率统计信息
func GetRateStatistics() map[string]interface{} {
	info := GetRateInfo()
	
	stats := map[string]interface{}{
		"rates": info,
		"summary": map[string]interface{}{
			"total_tokens": 3,
			"avg_rate": (GetUsdtCalcRate() + GetUsdcCalcRate() + GetTrxCalcRate()) / 3,
		},
	}
	
	return stats
}
