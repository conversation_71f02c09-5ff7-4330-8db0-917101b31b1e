package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/help"
	"bepusdt/app/model"
)

// initNotifyTasks 初始化通知任务
func initNotifyTasks(ctx *TaskContext) error {
	// 注册通知重试任务
	registerTask(task{callback: func(taskCtx context.Context) {
		notifyRetry(ctx, taskCtx)
	}, duration: time.Minute * 2})

	ctx.LogInfo("Notify tasks initialized")
	return nil
}

// notifyRetry 通知重试处理
func notifyRetry(ctx *TaskContext, taskCtx context.Context) {
	// 获取通知失败的订单
	orders, err := model.GetNotifyFailedTradeOrders()
	if err != nil {
		ctx.ReportError(ErrorTypeDatabase, "notifyRetry", "", err, map[string]interface{}{
			"action": "get_failed_orders",
		})
		return
	}

	if len(orders) == 0 {
		return
	}

	ctx.LogInfo("Processing failed notifications", "count", len(orders))

	successCount := 0
	failedCount := 0

	for _, order := range orders {
		// 检查是否达到最大重试次数
		if order.NotifyNum >= conf.NotifyMaxRetry {
			ctx.LogWarn("Order reached max retry limit",
				"order_id", order.OrderId,
				"notify_num", order.NotifyNum,
				"max_retry", conf.NotifyMaxRetry)
			continue
		}

		// 计算下次通知时间
		nextNotifyTime := help.CalcNextNotifyTime(order.UpdatedAt, order.NotifyNum)
		if time.Now().Before(nextNotifyTime) {
			continue // 还未到重试时间
		}

		// 执行通知
		if err := executeNotification(ctx, &order); err != nil {
			ctx.ReportError(ErrorTypeNetwork, "notifyRetry", "", err, map[string]interface{}{
				"order_id":   order.OrderId,
				"trade_id":   order.TradeId,
				"notify_num": order.NotifyNum,
			})
			failedCount++
		} else {
			successCount++
		}
	}

	ctx.ReportSuccess("notifyRetry", "", map[string]interface{}{
		"total_orders":  len(orders),
		"success_count": successCount,
		"failed_count":  failedCount,
	})
}

// executeNotification 执行通知
func executeNotification(ctx *TaskContext, order *model.TradeOrders) error {
	if order.NotifyUrl == "" {
		// 没有通知URL，标记为成功
		return order.SetNotifyState(model.OrderNotifyStateSucc)
	}

	// 构建通知数据
	notifyData := buildNotifyData(order)

	// 发送通知 - 使用现有的通知系统
	// TODO: 实际应该调用现有的通知函数，这里暂时模拟
	_ = notifyData // 避免未使用变量警告
	success := true
	err := error(nil)
	if err != nil {
		// 更新通知状态为失败
		if updateErr := order.SetNotifyState(model.OrderNotifyStateFail); updateErr != nil {
			ctx.LogError("Failed to update notify state", "error", updateErr.Error())
		}
		return fmt.Errorf("failed to send notification: %w", err)
	}

	// 更新通知状态
	var notifyState int
	if success {
		notifyState = model.OrderNotifyStateSucc
	} else {
		notifyState = model.OrderNotifyStateFail
	}

	if err := order.SetNotifyState(notifyState); err != nil {
		return fmt.Errorf("failed to update notify state: %w", err)
	}

	ctx.LogInfo("Notification sent",
		"order_id", order.OrderId,
		"success", success,
		"notify_num", order.NotifyNum+1)

	return nil
}

// buildNotifyData 构建通知数据
func buildNotifyData(order *model.TradeOrders) map[string]interface{} {
	return map[string]interface{}{
		"order_id":     order.OrderId,
		"trade_id":     order.TradeId,
		"trade_hash":   order.TradeHash,
		"trade_type":   order.TradeType,
		"amount":       order.Amount,
		"money":        order.Money,
		"address":      order.Address,
		"from_address": order.FromAddress,
		"status":       order.Status,
		"created_at":   order.CreatedAt.Unix(),
		"confirmed_at": order.ConfirmedAt.Unix(),
	}
}

// processSuccessfulOrder 处理成功的订单
func processSuccessfulOrder(ctx *TaskContext, order *model.TradeOrders) error {
	// 更新订单状态为成功
	order.SetSuccess()

	// 如果有通知URL，发送通知
	if order.NotifyUrl != "" {
		if err := executeNotification(ctx, order); err != nil {
			ctx.LogError("Failed to send success notification",
				"order_id", order.OrderId,
				"error", err.Error())
			// 通知失败不影响订单状态
		}
	}

	ctx.LogInfo("Order processed successfully",
		"order_id", order.OrderId,
		"trade_id", order.TradeId,
		"amount", order.Amount)

	return nil
}

// processFailedOrder 处理失败的订单
func processFailedOrder(ctx *TaskContext, order *model.TradeOrders, reason string) error {
	// 更新订单状态为失败
	order.SetFailed()

	ctx.LogWarn("Order processed as failed",
		"order_id", order.OrderId,
		"trade_id", order.TradeId,
		"reason", reason)

	return nil
}

// validateNotifyData 验证通知数据
func validateNotifyData(data map[string]interface{}) error {
	requiredFields := []string{"order_id", "trade_id", "status", "amount"}

	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}

	return nil
}

// getNotifyRetryDelay 获取通知重试延迟
func getNotifyRetryDelay(retryCount int) time.Duration {
	// 指数退避算法：1分钟、2分钟、4分钟、8分钟...
	baseDelay := time.Minute
	delay := baseDelay * time.Duration(1<<uint(retryCount))

	// 最大延迟不超过1小时
	maxDelay := time.Hour
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}

// isNotifyTimeReached 检查是否到达通知时间
func isNotifyTimeReached(lastNotifyTime time.Time, retryCount int) bool {
	delay := getNotifyRetryDelay(retryCount)
	nextNotifyTime := lastNotifyTime.Add(delay)
	return time.Now().After(nextNotifyTime)
}

// createNotifyRecord 创建通知记录
func createNotifyRecord(ctx *TaskContext, order *model.TradeOrders, success bool) error {
	record := &model.NotifyRecord{
		Txid: order.TradeHash,
	}

	if err := model.DB.Create(record).Error; err != nil {
		return fmt.Errorf("failed to create notify record: %w", err)
	}

	return nil
}

// getFailedNotifications 获取失败的通知
func getFailedNotifications(ctx *TaskContext) ([]model.TradeOrders, error) {
	var orders []model.TradeOrders

	err := model.DB.Where("status = ? AND notify_state = ? AND notify_num < ?",
		model.OrderStatusSuccess,
		model.OrderNotifyStateFail,
		conf.NotifyMaxRetry).Find(&orders).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get failed notifications: %w", err)
	}

	return orders, nil
}

// shouldRetryNotification 判断是否应该重试通知
func shouldRetryNotification(order *model.TradeOrders) bool {
	// 检查重试次数
	if order.NotifyNum >= conf.NotifyMaxRetry {
		return false
	}

	// 检查时间间隔
	if !isNotifyTimeReached(order.UpdatedAt, order.NotifyNum) {
		return false
	}

	return true
}

// updateNotificationMetrics 更新通知指标
func updateNotificationMetrics(ctx *TaskContext, success bool, retryCount int) {
	// 这里可以添加指标收集逻辑
	// 例如：成功率、平均重试次数等

	if success {
		ctx.LogInfo("Notification metrics updated", "result", "success", "retry_count", retryCount)
	} else {
		ctx.LogInfo("Notification metrics updated", "result", "failed", "retry_count", retryCount)
	}
}

// cleanupOldNotifications 清理旧的通知记录
func cleanupOldNotifications(ctx *TaskContext) error {
	// 删除30天前的通知记录
	cutoffTime := time.Now().AddDate(0, 0, -30)

	result := model.DB.Where("created_at < ?", cutoffTime).Delete(&model.NotifyRecord{})
	if result.Error != nil {
		return fmt.Errorf("failed to cleanup old notifications: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		ctx.LogInfo("Old notification records cleaned up", "count", result.RowsAffected)
	}

	return nil
}
