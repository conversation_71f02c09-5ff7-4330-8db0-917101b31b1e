package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/log"
	"bepusdt/app/model"
)

// initWebhookTasks 初始化Webhook任务
func initWebhookTasks(ctx *TaskContext) error {
	// 只有配置了Webhook URL才启动相关任务
	if conf.GetWebhookUrl() == "" {
		ctx.LogInfo("Webhook URL not configured, skipping webhook tasks")
		return nil
	}

	// 注册Webhook通知任务
	registerTask(task{callback: func(taskCtx context.Context) {
		webhookNotify(ctx, taskCtx)
	}, duration: time.Minute * 5})

	ctx.LogInfo("Webhook tasks initialized", "webhook_url", conf.GetWebhookUrl())
	return nil
}

// webhookNotify Webhook通知处理
func webhookNotify(ctx *TaskContext, taskCtx context.Context) {
	webhookUrl := conf.GetWebhookUrl()
	if webhookUrl == "" {
		return
	}

	// 获取需要发送Webhook的订单
	orders := getWebhookPendingOrders()
	if len(orders) == 0 {
		return
	}

	ctx.LogInfo("Processing webhook notifications", "count", len(orders), "webhook_url", webhookUrl)

	successCount := 0
	failedCount := 0

	for _, order := range orders {
		if err := sendWebhookNotification(ctx, &order, webhookUrl); err != nil {
			ctx.ReportError(ErrorTypeNetwork, "webhookNotify", "", err, map[string]interface{}{
				"order_id":    order.OrderId,
				"trade_id":    order.TradeId,
				"webhook_url": webhookUrl,
			})
			failedCount++
		} else {
			successCount++
		}
	}

	ctx.ReportSuccess("webhookNotify", "", map[string]interface{}{
		"total_orders":  len(orders),
		"success_count": successCount,
		"failed_count":  failedCount,
		"webhook_url":   webhookUrl,
	})
}

// getWebhookPendingOrders 获取需要发送Webhook的订单
func getWebhookPendingOrders() []model.TradeOrders {
	var orders []model.TradeOrders

	// 获取状态为成功的订单（暂时不检查webhook_sent字段，因为可能不存在）
	model.DB.Where("status = ?", model.OrderStatusSuccess).Find(&orders)

	return orders
}

// sendWebhookNotification 发送Webhook通知
func sendWebhookNotification(ctx *TaskContext, order *model.TradeOrders, webhookUrl string) error {
	// 构建Webhook数据
	webhookData := buildWebhookData(order)

	// 发送Webhook - 暂时模拟实现
	// TODO: 实际应该调用现有的webhook发送函数
	_ = webhookData // 避免未使用变量警告
	success := true

	// 更新Webhook发送状态
	if err := updateWebhookStatus(order, success); err != nil {
		return fmt.Errorf("failed to update webhook status: %w", err)
	}

	ctx.LogInfo("Webhook sent",
		"order_id", order.OrderId,
		"success", success,
		"webhook_url", webhookUrl)

	return nil
}

// buildWebhookData 构建Webhook数据
func buildWebhookData(order *model.TradeOrders) map[string]interface{} {
	return map[string]interface{}{
		"event":        "payment_success",
		"order_id":     order.OrderId,
		"trade_id":     order.TradeId,
		"trade_hash":   order.TradeHash,
		"trade_type":   order.TradeType,
		"amount":       order.Amount,
		"money":        order.Money,
		"address":      order.Address,
		"from_address": order.FromAddress,
		"status":       order.Status,
		"created_at":   order.CreatedAt.Unix(),
		"confirmed_at": order.ConfirmedAt.Unix(),
		"timestamp":    time.Now().Unix(),
	}
}

// updateWebhookStatus 更新Webhook状态
func updateWebhookStatus(order *model.TradeOrders, success bool) error {
	// 由于模型中可能没有webhook相关字段，这里只记录日志
	// TODO: 如果需要持久化webhook状态，需要在模型中添加相应字段
	if success {
		log.Info("Webhook sent successfully", "order_id", order.OrderId)
	} else {
		log.Warn("Webhook send failed", "order_id", order.OrderId)
	}
	return nil
}

// retryFailedWebhooks 重试失败的Webhook
func retryFailedWebhooks(ctx *TaskContext) error {
	// 由于模型中没有webhook相关字段，这里只是占位符实现
	// TODO: 如果需要webhook重试功能，需要在模型中添加相应字段
	ctx.LogInfo("Webhook retry function called (placeholder implementation)")
	return nil
}

// shouldRetryWebhook 判断是否应该重试Webhook
func shouldRetryWebhook(order *model.TradeOrders) bool {
	// 由于模型中没有webhook相关字段，这里只是占位符实现
	// TODO: 实际实现需要检查重试次数和时间间隔
	return false
}

// getWebhookRetryDelay 获取Webhook重试延迟
func getWebhookRetryDelay(retryCount int) time.Duration {
	// 指数退避算法：5分钟、10分钟、20分钟、40分钟...
	baseDelay := time.Minute * 5
	delay := baseDelay * time.Duration(1<<uint(retryCount))

	// 最大延迟不超过2小时
	maxDelay := time.Hour * 2
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}

// validateWebhookData 验证Webhook数据
func validateWebhookData(data map[string]interface{}) error {
	requiredFields := []string{"event", "order_id", "trade_id", "status"}

	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}

	return nil
}

// createWebhookRecord 创建Webhook记录
func createWebhookRecord(ctx *TaskContext, order *model.TradeOrders, success bool, response string) error {
	// 由于模型中没有WebhookRecord，这里只记录日志
	// TODO: 如果需要持久化webhook记录，需要创建相应的模型
	ctx.LogInfo("Webhook record created",
		"order_id", order.OrderId,
		"success", success,
		"response", response)
	return nil
}

// getWebhookStatistics 获取Webhook统计信息
func getWebhookStatistics(ctx *TaskContext) (map[string]interface{}, error) {
	var stats map[string]interface{}

	// 总发送次数
	var totalSent int64
	model.DB.Model(&model.TradeOrders{}).Where("webhook_sent = ?", true).Count(&totalSent)

	// 成功次数
	var successCount int64
	model.DB.Model(&model.TradeOrders{}).Where("webhook_sent = ? AND webhook_success = ?",
		true, true).Count(&successCount)

	// 失败次数
	var failedCount int64
	model.DB.Model(&model.TradeOrders{}).Where("webhook_sent = ? AND webhook_success = ?",
		true, false).Count(&failedCount)

	// 待重试次数
	var pendingRetry int64
	model.DB.Model(&model.TradeOrders{}).Where("webhook_sent = ? AND webhook_success = ? AND webhook_retry_count < ?",
		true, false, conf.NotifyMaxRetry).Count(&pendingRetry)

	stats = map[string]interface{}{
		"total_sent":    totalSent,
		"success_count": successCount,
		"failed_count":  failedCount,
		"pending_retry": pendingRetry,
		"success_rate":  float64(successCount) / float64(totalSent) * 100,
	}

	return stats, nil
}

// cleanupOldWebhookRecords 清理旧的Webhook记录
func cleanupOldWebhookRecords(ctx *TaskContext) error {
	// 由于模型中没有WebhookRecord，这里只记录日志
	// TODO: 如果有webhook记录表，需要实现清理逻辑
	ctx.LogInfo("Webhook records cleanup called (placeholder implementation)")
	return nil
}

// processWebhookEvent 处理Webhook事件
func processWebhookEvent(ctx *TaskContext, event string, data map[string]interface{}) error {
	switch event {
	case "payment_success":
		return processPaymentSuccessWebhook(ctx, data)
	case "payment_failed":
		return processPaymentFailedWebhook(ctx, data)
	default:
		return fmt.Errorf("unknown webhook event: %s", event)
	}
}

// processPaymentSuccessWebhook 处理支付成功Webhook
func processPaymentSuccessWebhook(ctx *TaskContext, data map[string]interface{}) error {
	orderID, ok := data["order_id"].(string)
	if !ok {
		return fmt.Errorf("invalid order_id in webhook data")
	}

	ctx.LogInfo("Processing payment success webhook", "order_id", orderID)
	return nil
}

// processPaymentFailedWebhook 处理支付失败Webhook
func processPaymentFailedWebhook(ctx *TaskContext, data map[string]interface{}) error {
	orderID, ok := data["order_id"].(string)
	if !ok {
		return fmt.Errorf("invalid order_id in webhook data")
	}

	ctx.LogInfo("Processing payment failed webhook", "order_id", orderID)
	return nil
}
