package task_v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"bepusdt/app/log"

	"github.com/smallnest/chanx"
)

// QueueManager 队列管理器实现
type QueueManager struct {
	transferQueue *chanx.UnboundedChan[[]Transfer]
	resourceQueue *chanx.UnboundedChan[[]Resource]
	notOrderQueue *chanx.UnboundedChan[[]Transfer]
	stats         map[string]*QueueStats
	mu            sync.RWMutex
}

// transferQueueImpl 转账队列实现
type transferQueueImpl struct {
	queue *chanx.UnboundedChan[[]Transfer]
	qm    *QueueManager
	name  string
}

// resourceQueueImpl 资源队列实现
type resourceQueueImpl struct {
	queue *chanx.UnboundedChan[[]Resource]
	qm    *QueueManager
	name  string
}

// NewQueueManager 创建新的队列管理器
func NewQueueManager() *QueueManager {
	ctx := context.Background()
	qm := &QueueManager{
		transferQueue: chanx.NewUnboundedChan[[]Transfer](ctx, 30),
		resourceQueue: chanx.NewUnboundedChan[[]Resource](ctx, 30),
		notOrderQueue: chanx.NewUnboundedChan[[]Transfer](ctx, 30),
		stats:         make(map[string]*QueueStats),
	}

	// 初始化统计信息
	qm.initStats()

	return qm
}

// initStats 初始化统计信息
func (qm *QueueManager) initStats() {
	now := time.Now()
	qm.stats["transfer"] = &QueueStats{LastProcessTime: now}
	qm.stats["resource"] = &QueueStats{LastProcessTime: now}
	qm.stats["notOrder"] = &QueueStats{LastProcessTime: now}
}

// GetTransferQueue 获取转账队列
func (qm *QueueManager) GetTransferQueue() TransferQueue {
	return &transferQueueImpl{
		queue: qm.transferQueue,
		qm:    qm,
		name:  "transfer",
	}
}

// GetResourceQueue 获取资源队列
func (qm *QueueManager) GetResourceQueue() ResourceQueue {
	return &resourceQueueImpl{
		queue: qm.resourceQueue,
		qm:    qm,
		name:  "resource",
	}
}

// GetNotOrderQueue 获取非订单队列
func (qm *QueueManager) GetNotOrderQueue() TransferQueue {
	return &transferQueueImpl{
		queue: qm.notOrderQueue,
		qm:    qm,
		name:  "notOrder",
	}
}

// GetStats 获取队列统计信息
func (qm *QueueManager) GetStats(queueName string) *QueueStats {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	if stats, exists := qm.stats[queueName]; exists {
		// 返回副本，避免并发修改
		return &QueueStats{
			TotalMessages:   stats.TotalMessages,
			ProcessedCount:  stats.ProcessedCount,
			ErrorCount:      stats.ErrorCount,
			LastProcessTime: stats.LastProcessTime,
		}
	}
	return nil
}

// UpdateStats 更新统计信息
func (qm *QueueManager) UpdateStats(queueName string, processed bool) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if stats, exists := qm.stats[queueName]; exists {
		stats.TotalMessages++
		stats.LastProcessTime = time.Now()
		if processed {
			stats.ProcessedCount++
		} else {
			stats.ErrorCount++
		}
	}
}

// GetAllStats 获取所有队列统计信息
func (qm *QueueManager) GetAllStats() map[string]*QueueStats {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	allStats := make(map[string]*QueueStats)
	for name, stats := range qm.stats {
		allStats[name] = &QueueStats{
			TotalMessages:   stats.TotalMessages,
			ProcessedCount:  stats.ProcessedCount,
			ErrorCount:      stats.ErrorCount,
			LastProcessTime: stats.LastProcessTime,
		}
	}
	return allStats
}

// ResetStats 重置统计信息
func (qm *QueueManager) ResetStats(queueName string) {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if stats, exists := qm.stats[queueName]; exists {
		stats.TotalMessages = 0
		stats.ProcessedCount = 0
		stats.ErrorCount = 0
		stats.LastProcessTime = time.Now()
	}
}

// transferQueueImpl 实现 TransferQueue 接口

// Send 发送转账数据到队列
func (tq *transferQueueImpl) Send(transfers []Transfer) error {
	if len(transfers) == 0 {
		return nil
	}

	select {
	case tq.queue.In <- transfers:
		tq.qm.UpdateStats(tq.name, true)
		log.Info("Transfers sent to queue", "queue", tq.name, "count", len(transfers))
		return nil
	default:
		tq.qm.UpdateStats(tq.name, false)
		log.Warn("Failed to send transfers to queue", "queue", tq.name, "count", len(transfers))
		return fmt.Errorf("queue %s is full", tq.name)
	}
}

// Receive 从队列接收转账数据
func (tq *transferQueueImpl) Receive() <-chan []Transfer {
	return tq.queue.Out
}

// resourceQueueImpl 实现 ResourceQueue 接口

// Send 发送资源数据到队列
func (rq *resourceQueueImpl) Send(resources []Resource) error {
	if len(resources) == 0 {
		return nil
	}

	select {
	case rq.queue.In <- resources:
		rq.qm.UpdateStats(rq.name, true)
		log.Info("Resources sent to queue", "queue", rq.name, "count", len(resources))
		return nil
	default:
		rq.qm.UpdateStats(rq.name, false)
		log.Warn("Failed to send resources to queue", "queue", rq.name, "count", len(resources))
		return fmt.Errorf("queue %s is full", rq.name)
	}
}

// Receive 从队列接收资源数据
func (rq *resourceQueueImpl) Receive() <-chan []Resource {
	return rq.queue.Out
}

// Close 关闭队列管理器
func (qm *QueueManager) Close() error {
	// chanx.UnboundedChan 没有 Close 方法，这里只是记录日志
	log.Info("Queue manager closed")
	return nil
}

// GetQueueLength 获取队列长度
func (qm *QueueManager) GetQueueLength(queueName string) int {
	switch queueName {
	case "transfer":
		return len(qm.transferQueue.In)
	case "resource":
		return len(qm.resourceQueue.In)
	case "notOrder":
		return len(qm.notOrderQueue.In)
	default:
		return -1
	}
}

// IsQueueEmpty 检查队列是否为空
func (qm *QueueManager) IsQueueEmpty(queueName string) bool {
	return qm.GetQueueLength(queueName) == 0
}

// GetQueueCapacity 获取队列容量
func (qm *QueueManager) GetQueueCapacity(queueName string) int {
	switch queueName {
	case "transfer", "resource", "notOrder":
		return 30 // 默认容量
	default:
		return -1
	}
}
