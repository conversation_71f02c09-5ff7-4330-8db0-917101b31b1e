package task_v2

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/log"

	"github.com/smallnest/chanx"
)

// initEVMNetworks 初始化所有EVM网络
func initEVMNetworks(ctx *TaskContext) error {
	if ctx == nil {
		return fmt.Errorf("task context is nil")
	}

	networks := []string{conf.Ethereum, conf.Bsc, conf.Polygon, conf.Arbitrum, conf.Xlayer, conf.Base}

	for _, network := range networks {
		if err := initEVMNetwork(network, ctx); err != nil {
			return fmt.Errorf("failed to init EVM network %s: %w", network, err)
		}
	}

	log.Info("All EVM networks initialized successfully")
	return nil
}

// initEVMNetwork 初始化单个EVM网络
func initEVMNetwork(network string, ctx *TaskContext) error {
	config, exists := ctx.GetConfig(network)
	if !exists {
		return fmt.Errorf("config not found for network: %s", network)
	}

	// 配置验证
	if err := config.Validate(); err != nil {
		return fmt.Errorf("invalid config for network %s: %w", network, err)
	}

	// 健康检查（如果启用）
	if config.HealthCheck {
		if err := checkEndpointHealth(config.Endpoint); err != nil {
			ctx.LogWarn("Primary endpoint unhealthy, trying fallback",
				"network", network, "error", err.Error())

			if config.FallbackEndpoint != "" {
				if err := checkEndpointHealth(config.FallbackEndpoint); err == nil {
					// 更新配置使用备用端点
					config.Endpoint = config.FallbackEndpoint
					if err := ctx.UpdateConfig(network, config); err != nil {
						ctx.LogError("Failed to update config with fallback endpoint",
							"network", network, "error", err.Error())
					} else {
						ctx.LogInfo("Switched to fallback endpoint", "network", network)
					}
				} else {
					ctx.LogError("Both primary and fallback endpoints unhealthy",
						"network", network)
				}
			}
		}
	}

	// 创建EVM监控器
	monitor, err := newEVMMonitor(network, config, ctx)
	if err != nil {
		return fmt.Errorf("failed to create EVM monitor for %s: %w", network, err)
	}

	// 注册任务
	registerTask(task{callback: monitor.blockDispatch})
	registerTask(task{callback: monitor.blockRoll, duration: config.RollInterval})
	registerTask(task{callback: monitor.tradeConfirmHandle, duration: config.ConfirmInterval})

	// 注册配置变更监听器
	if configMgr, ok := ctx.GetConfigProvider().(*ConfigManager); ok {
		configMgr.RegisterChangeListener(monitor.onConfigChange)
	}

	ctx.LogInfo("EVM network initialized", "network", network, "endpoint", config.Endpoint)
	return nil
}

// checkEndpointHealth 检查端点健康状态
func checkEndpointHealth(endpoint string) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	post := []byte(`{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`)
	req, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer(post))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("unhealthy status code: %d", resp.StatusCode)
	}

	return nil
}

// evmMonitor EVM监控器结构
type evmMonitor struct {
	network        string
	config         BlockchainConfig
	blockScanQueue *chanx.UnboundedChan[evmBlock]
	ctx            *TaskContext
}

// evmBlock EVM区块范围
type evmBlock struct {
	From int64
	To   int64
}

// newEVMMonitor 创建新的EVM监控器
func newEVMMonitor(network string, config BlockchainConfig, ctx *TaskContext) (*evmMonitor, error) {
	if ctx == nil {
		return nil, fmt.Errorf("task context is nil")
	}

	return &evmMonitor{
		network:        network,
		config:         config,
		blockScanQueue: chanx.NewUnboundedChan[evmBlock](context.Background(), 30),
		ctx:            ctx,
	}, nil
}

// GetNetwork 获取网络名称
func (em *evmMonitor) GetNetwork() string {
	return em.network
}

// UpdateConfig 更新配置
func (em *evmMonitor) UpdateConfig(config BlockchainConfig) error {
	if config.Network != em.network {
		return fmt.Errorf("network mismatch: expected %s, got %s", em.network, config.Network)
	}

	em.config = config
	em.ctx.LogInfo("EVM monitor config updated", "network", em.network)
	return nil
}

// onConfigChange 配置变更监听器
func (em *evmMonitor) onConfigChange(network string, oldConfig, newConfig BlockchainConfig) {
	if network == em.network {
		em.config = newConfig
		em.ctx.LogInfo("EVM monitor config changed",
			"network", network,
			"old_endpoint", oldConfig.Endpoint,
			"new_endpoint", newConfig.Endpoint)
	}
}

// Start 启动监控器
func (em *evmMonitor) Start(ctx context.Context) error {
	em.ctx.LogInfo("Starting EVM monitor", "network", em.network)
	// 这里可以添加启动逻辑
	return nil
}

// Stop 停止监控器
func (em *evmMonitor) Stop() error {
	em.ctx.LogInfo("Stopping EVM monitor", "network", em.network)
	// 这里可以添加停止逻辑
	return nil
}

// tryFallbackEndpoint 尝试切换到备用端点
func (em *evmMonitor) tryFallbackEndpoint() {
	if em.config.FallbackEndpoint != "" {
		em.ctx.LogInfo("Switching to fallback endpoint",
			"network", em.network,
			"fallback", em.config.FallbackEndpoint)

		// 更新配置
		newConfig := em.config
		newConfig.Endpoint = em.config.FallbackEndpoint
		if err := em.ctx.UpdateConfig(em.network, newConfig); err != nil {
			em.ctx.LogError("Failed to update config with fallback endpoint",
				"network", em.network, "error", err.Error())
		}
	}
}

// reportError 报告错误的便捷方法
func (em *evmMonitor) reportError(errorType ErrorType, taskName string, err error, context map[string]interface{}) bool {
	if context == nil {
		context = make(map[string]interface{})
	}
	context["endpoint"] = em.config.Endpoint
	return em.ctx.ReportError(errorType, taskName, em.network, err, context)
}

// reportSuccess 报告成功的便捷方法
func (em *evmMonitor) reportSuccess(taskName string, context map[string]interface{}) {
	if context == nil {
		context = make(map[string]interface{})
	}
	context["endpoint"] = em.config.Endpoint
	em.ctx.ReportSuccess(taskName, em.network, context)
}
