package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/log"

	"github.com/shopspring/decimal"
)

// ExampleUsage 展示如何使用新的任务系统
func ExampleUsage() {
	// 1. 初始化系统
	if err := Init(); err != nil {
		log.Error("Failed to initialize task system", "error", err.Error())
		return
	}

	// 2. 验证系统状态
	if err := ValidateSystem(); err != nil {
		log.Error("System validation failed", "error", err.Error())
		return
	}

	// 3. 获取系统统计信息
	stats := GetSystemStats()
	log.Info("System statistics", "stats", stats)

	// 4. 启动系统
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go Start(ctx)

	// 5. 运行一段时间后停止
	time.Sleep(time.Second * 10)

	if err := Stop(); err != nil {
		log.Error("Failed to stop system", "error", err.Error())
	}

	log.Info("Example usage completed")
}

// ExampleCompatibilityUsage 展示兼容性使用方法
func ExampleCompatibilityUsage() {
	// 1. 兼容性初始化
	if err := CompatibleInit(); err != nil {
		log.Error("Failed to initialize with compatibility", "error", err.Error())
		return
	}

	// 2. 检查当前使用的系统
	if IsNewSystemEnabled() {
		log.Info("Using new task system")
	} else {
		log.Info("Using old task system")
	}

	// 3. 兼容性启动
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go CompatibleStart(ctx)

	// 4. 获取迁移状态
	status := GetMigrationStatus()
	log.Info("Migration status", "status", status)

	time.Sleep(time.Second * 5)
	log.Info("Compatibility example completed")
}

// ExampleMigration 展示迁移功能
func ExampleMigration() {
	log.Info("Starting migration example")

	// 1. 执行渐进式迁移
	if err := PerformGradualMigration(); err != nil {
		log.Error("Migration failed", "error", err.Error())
		return
	}

	// 2. 验证迁移
	if err := ValidateMigration(); err != nil {
		log.Error("Migration validation failed", "error", err.Error())

		// 3. 如果验证失败，回滚
		if err := RollbackMigration(); err != nil {
			log.Error("Rollback failed", "error", err.Error())
		} else {
			log.Info("Successfully rolled back")
		}
		return
	}

	log.Info("Migration completed successfully")

	// 4. 获取兼容性信息
	info := GetCompatibilityInfo()
	log.Info("Compatibility info", "info", info)
}

// ExampleConfigManagement 展示配置管理功能
func ExampleConfigManagement() {
	log.Info("Starting config management example")

	// 1. 获取网络配置
	config, err := GetNetworkConfig("ethereum")
	if err != nil {
		log.Error("Failed to get config", "error", err.Error())
		return
	}

	log.Info("Current Ethereum config", "config", config)

	// 2. 更新配置
	newConfig := config
	newConfig.RollInterval = time.Second * 15 // 修改轮询间隔

	if err := UpdateNetworkConfig("ethereum", newConfig); err != nil {
		log.Error("Failed to update config", "error", err.Error())
		return
	}

	log.Info("Config updated successfully")

	// 3. 重新加载所有配置
	if err := ReloadConfigs(); err != nil {
		log.Error("Failed to reload configs", "error", err.Error())
		return
	}

	log.Info("All configs reloaded")
}

// ExampleErrorHandling 展示错误处理功能
func ExampleErrorHandling() {
	log.Info("Starting error handling example")

	// 获取任务上下文
	ctx := GetTaskContext()
	if ctx == nil {
		log.Error("Task context not initialized")
		return
	}

	// 1. 报告不同类型的错误
	ctx.ReportError(ErrorTypeNetwork, "example_task", "ethereum",
		fmt.Errorf("connection timeout"), map[string]interface{}{
			"endpoint": "https://example.com",
			"timeout":  "30s",
		})

	ctx.ReportError(ErrorTypeRPC, "example_task", "ethereum",
		fmt.Errorf("invalid response"), map[string]interface{}{
			"method": "eth_blockNumber",
		})

	// 2. 报告成功
	ctx.ReportSuccess("example_task", "ethereum", map[string]interface{}{
		"processed_blocks": 10,
		"duration":         "2s",
	})

	log.Info("Error handling example completed")
}

// ExampleQueueManagement 展示队列管理功能
func ExampleQueueManagement() {
	log.Info("Starting queue management example")

	// 获取任务上下文
	ctx := GetTaskContext()
	if ctx == nil {
		log.Error("Task context not initialized")
		return
	}

	// 1. 发送数据到队列
	transfers := []Transfer{
		{
			Network:     "ethereum",
			TxHash:      "0x123...",
			Amount:      decimal.NewFromFloat(100.0),
			RecvAddress: "0xabc...",
			TradeType:   "usdt.erc20",
			Timestamp:   time.Now(),
			BlockNum:    12345,
		},
	}

	if err := ctx.SendToTransferQueue(transfers); err != nil {
		log.Error("Failed to send to transfer queue", "error", err.Error())
		return
	}

	// 2. 获取队列统计
	stats := ctx.GetQueueStats("transfer")
	if stats != nil {
		log.Info("Transfer queue stats", "stats", stats)
	}

	log.Info("Queue management example completed")
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	log.Info("Running all examples")

	// 基本使用示例
	ExampleUsage()

	// 兼容性使用示例
	ExampleCompatibilityUsage()

	// 迁移示例
	ExampleMigration()

	// 配置管理示例
	ExampleConfigManagement()

	// 错误处理示例
	ExampleErrorHandling()

	// 队列管理示例
	ExampleQueueManagement()

	log.Info("All examples completed")
}
