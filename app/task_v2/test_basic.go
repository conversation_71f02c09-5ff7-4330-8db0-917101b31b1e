package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/log"
)

// TestBasicInitialization 测试基本初始化功能
func TestBasicInitialization() error {
	log.Info("Starting basic initialization test")

	// 1. 测试初始化
	if err := Init(); err != nil {
		return fmt.Errorf("initialization failed: %w", err)
	}

	// 2. 验证初始化状态
	if !IsInitialized() {
		return fmt.Errorf("system not initialized")
	}

	// 3. 验证任务系统
	if !IsTaskSystemInitialized() {
		return fmt.Errorf("task system not initialized")
	}

	// 4. 获取系统统计
	stats := GetSystemStats()
	if stats == nil {
		return fmt.Errorf("failed to get system stats")
	}

	log.Info("System stats", "stats", stats)

	// 5. 验证系统
	if err := ValidateSystem(); err != nil {
		return fmt.Errorf("system validation failed: %w", err)
	}

	log.Info("Basic initialization test passed")
	return nil
}

// TestConfigManagement 测试配置管理功能
func TestConfigManagement() error {
	log.Info("Starting config management test")

	// 1. 获取支持的网络
	networks := GetSupportedNetworks()
	if len(networks) == 0 {
		return fmt.Errorf("no supported networks found")
	}

	log.Info("Supported networks", "networks", networks)

	// 2. 测试获取网络配置
	for _, network := range networks {
		config, err := GetNetworkConfig(network)
		if err != nil {
			return fmt.Errorf("failed to get config for %s: %w", network, err)
		}

		log.Info("Network config", "network", network, "config", config)

		// 3. 测试配置更新
		originalInterval := config.RollInterval
		config.RollInterval = time.Second * 10

		if err := UpdateNetworkConfig(network, config); err != nil {
			return fmt.Errorf("failed to update config for %s: %w", network, err)
		}

		// 4. 验证配置更新
		updatedConfig, err := GetNetworkConfig(network)
		if err != nil {
			return fmt.Errorf("failed to get updated config for %s: %w", network, err)
		}

		if updatedConfig.RollInterval != time.Second*10 {
			return fmt.Errorf("config update failed for %s", network)
		}

		// 5. 恢复原始配置
		config.RollInterval = originalInterval
		if err := UpdateNetworkConfig(network, config); err != nil {
			return fmt.Errorf("failed to restore config for %s: %w", network, err)
		}
	}

	log.Info("Config management test passed")
	return nil
}

// TestTaskExecution 测试任务执行功能
func TestTaskExecution() error {
	log.Info("Starting task execution test")

	// 1. 启动任务系统
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	go Start(ctx)

	// 2. 等待任务运行
	time.Sleep(time.Second * 2)

	// 3. 检查任务状态
	taskCount := GetTaskCount()
	if taskCount == 0 {
		return fmt.Errorf("no tasks running")
	}

	log.Info("Tasks running", "count", taskCount)

	// 4. 等待任务完成
	<-ctx.Done()

	log.Info("Task execution test passed")
	return nil
}

// TestErrorHandling 测试错误处理功能
func TestErrorHandling() error {
	log.Info("Starting error handling test")

	// 1. 获取任务上下文
	ctx := GetTaskContext()
	if ctx == nil {
		return fmt.Errorf("task context not available")
	}

	// 2. 测试错误报告
	testErr := fmt.Errorf("test error")
	shouldRetry := ctx.ReportError(ErrorTypeNetwork, "test_task", "ethereum", testErr, map[string]interface{}{
		"test_key": "test_value",
	})

	log.Info("Error reported", "should_retry", shouldRetry)

	// 3. 测试成功报告
	ctx.ReportSuccess("test_task", "ethereum", map[string]interface{}{
		"result": "success",
	})

	log.Info("Error handling test passed")
	return nil
}

// TestCompatibility 测试兼容性功能
func TestCompatibility() error {
	log.Info("Starting compatibility test")

	// 1. 测试迁移状态
	status := GetMigrationStatus()
	log.Info("Migration status", "status", status)

	// 2. 测试兼容性信息
	info := GetCompatibilityInfo()
	log.Info("Compatibility info", "info", info)

	// 3. 测试迁移功能（如果新系统未启用）
	if !IsNewSystemEnabled() {
		if err := TestMigration(); err != nil {
			return fmt.Errorf("migration test failed: %w", err)
		}
	}

	log.Info("Compatibility test passed")
	return nil
}

// RunAllTests 运行所有测试
func RunAllTests() error {
	log.Info("Starting all tests")

	tests := []struct {
		name string
		fn   func() error
	}{
		{"BasicInitialization", TestBasicInitialization},
		{"ConfigManagement", TestConfigManagement},
		{"TaskExecution", TestTaskExecution},
		{"ErrorHandling", TestErrorHandling},
		{"Compatibility", TestCompatibility},
	}

	for _, test := range tests {
		log.Info("Running test", "name", test.name)
		if err := test.fn(); err != nil {
			return fmt.Errorf("test %s failed: %w", test.name, err)
		}
		log.Info("Test passed", "name", test.name)
	}

	log.Info("All tests passed")
	return nil
}

// BenchmarkSystemPerformance 性能基准测试
func BenchmarkSystemPerformance() error {
	log.Info("Starting performance benchmark")

	// 1. 初始化系统
	start := time.Now()
	if err := Init(); err != nil {
		return fmt.Errorf("initialization failed: %w", err)
	}
	initTime := time.Since(start)

	// 2. 配置操作性能测试
	start = time.Now()
	networks := GetSupportedNetworks()
	for i := 0; i < 100; i++ {
		for _, network := range networks {
			_, err := GetNetworkConfig(network)
			if err != nil {
				return fmt.Errorf("config retrieval failed: %w", err)
			}
		}
	}
	configTime := time.Since(start)

	// 3. 系统验证性能测试
	start = time.Now()
	for i := 0; i < 10; i++ {
		if err := ValidateSystem(); err != nil {
			return fmt.Errorf("validation failed: %w", err)
		}
	}
	validationTime := time.Since(start)

	log.Info("Performance benchmark results",
		"init_time", initTime,
		"config_ops_time", configTime,
		"validation_time", validationTime)

	return nil
}

// TestSystemStability 系统稳定性测试
func TestSystemStability() error {
	log.Info("Starting system stability test")

	// 1. 长时间运行测试
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	go Start(ctx)

	// 2. 定期检查系统状态
	ticker := time.NewTicker(time.Second * 5)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Info("System stability test completed")
			return nil
		case <-ticker.C:
			if err := ValidateSystem(); err != nil {
				return fmt.Errorf("system became unstable: %w", err)
			}

			stats := GetSystemStats()
			log.Info("System status check", "stats", stats)
		}
	}
}

// TestCleanup 清理测试资源
func TestCleanup() error {
	log.Info("Starting cleanup")

	// 1. 停止系统
	if err := Stop(); err != nil {
		return fmt.Errorf("failed to stop system: %w", err)
	}

	// 2. 清理迁移资源
	if err := CleanupMigration(); err != nil {
		return fmt.Errorf("failed to cleanup migration: %w", err)
	}

	log.Info("Cleanup completed")
	return nil
}

// QuickTest 快速测试（用于开发时验证）
func QuickTest() error {
	log.Info("Starting quick test")

	if err := TestBasicInitialization(); err != nil {
		return err
	}

	if err := TestConfigManagement(); err != nil {
		return err
	}

	if err := TestCleanup(); err != nil {
		return err
	}

	log.Info("Quick test passed")
	return nil
}
