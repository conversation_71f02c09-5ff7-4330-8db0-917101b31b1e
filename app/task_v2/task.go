package task_v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"bepusdt/app/log"
)

// task 任务结构
type task struct {
	duration time.Duration
	callback func(ctx context.Context)
}

var (
	tasks []task
	mu    sync.Mutex
)

// Init 初始化任务系统
func Init() error {
	// 初始化任务上下文
	if err := InitTaskContext(); err != nil {
		return fmt.Errorf("failed to init task context: %w", err)
	}

	ctx := MustGetTaskContext()

	// 初始化EVM网络
	if err := initEVMNetworks(ctx); err != nil {
		return fmt.Errorf("failed to init EVM networks: %w", err)
	}

	// 初始化非EVM网络
	if err := initNonEVMNetworks(ctx); err != nil {
		return fmt.Errorf("failed to init non-EVM networks: %w", err)
	}

	// 初始化其他任务
	if err := initOtherTasks(ctx); err != nil {
		return fmt.Errorf("failed to init other tasks: %w", err)
	}

	log.Info("Task system v2 initialized successfully")
	return nil
}

// initNonEVMNetworks 初始化非EVM网络
func initNonEVMNetworks(ctx *TaskContext) error {
	// 初始化Tron
	if err := initTronNetwork(ctx); err != nil {
		return fmt.Errorf("failed to init Tron: %w", err)
	}

	// 初始化Solana
	if err := initSolanaNetwork(ctx); err != nil {
		return fmt.Errorf("failed to init Solana: %w", err)
	}

	// 初始化Aptos
	if err := initAptosNetwork(ctx); err != nil {
		return fmt.Errorf("failed to init Aptos: %w", err)
	}

	return nil
}

// initOtherTasks 初始化其他任务
func initOtherTasks(ctx *TaskContext) error {
	// 初始化转账处理任务
	if err := initTransferTasks(ctx); err != nil {
		return fmt.Errorf("failed to init transfer tasks: %w", err)
	}

	// 初始化通知任务
	if err := initNotifyTasks(ctx); err != nil {
		return fmt.Errorf("failed to init notify tasks: %w", err)
	}

	// 初始化Webhook任务
	if err := initWebhookTasks(ctx); err != nil {
		return fmt.Errorf("failed to init webhook tasks: %w", err)
	}

	// 初始化Bot任务
	if err := initBotTasks(ctx); err != nil {
		return fmt.Errorf("failed to init bot tasks: %w", err)
	}

	// 初始化汇率任务
	if err := initRateTasks(ctx); err != nil {
		return fmt.Errorf("failed to init rate tasks: %w", err)
	}

	return nil
}

// registerTask 注册任务
func registerTask(t task) {
	mu.Lock()
	defer mu.Unlock()

	if t.callback == nil {
		panic("task callback cannot be nil")
	}

	tasks = append(tasks, t)
}

// Start 启动任务系统
func Start(ctx context.Context) {
	mu.Lock()
	defer mu.Unlock()

	log.Info("Starting task system v2", "task_count", len(tasks))

	for i, t := range tasks {
		go func(taskIndex int, t task) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Task panic recovered", "task_index", taskIndex, "panic", r)
				}
			}()

			if t.duration <= 0 {
				// 一次性任务
				t.callback(ctx)
				return
			}

			// 定时任务
			t.callback(ctx)

			ticker := time.NewTicker(t.duration)
			defer ticker.Stop()

			for {
				select {
				case <-ctx.Done():
					log.Info("Task stopped due to context cancellation", "task_index", taskIndex)
					return
				case <-ticker.C:
					t.callback(ctx)
				}
			}
		}(i, t)
	}

	log.Info("All tasks started")
}

// GetTaskCount 获取任务数量
func GetTaskCount() int {
	mu.Lock()
	defer mu.Unlock()
	return len(tasks)
}

// IsInitialized 检查任务系统是否已初始化
func IsTaskSystemInitialized() bool {
	return IsInitialized() && len(tasks) > 0
}

// Stop 停止任务系统
func Stop() error {
	if globalContext != nil {
		if err := globalContext.Close(); err != nil {
			return fmt.Errorf("failed to close task context: %w", err)
		}
	}

	log.Info("Task system v2 stopped")
	return nil
}

// GetSystemStats 获取系统统计信息
func GetSystemStats() map[string]interface{} {
	ctx := GetTaskContext()
	if ctx == nil {
		return map[string]interface{}{
			"initialized": false,
		}
	}

	stats := map[string]interface{}{
		"initialized": true,
		"task_count":  GetTaskCount(),
		"networks":    GetSupportedNetworks(),
	}

	// 添加队列统计
	if qm, ok := ctx.GetQueueProvider().(*QueueManager); ok {
		stats["queues"] = qm.GetAllStats()
	}

	return stats
}

// ValidateSystem 验证系统状态
func ValidateSystem() error {
	if !IsTaskSystemInitialized() {
		return fmt.Errorf("task system not initialized")
	}

	ctx := GetTaskContext()
	if ctx == nil {
		return fmt.Errorf("task context is nil")
	}

	if err := ctx.Validate(); err != nil {
		return fmt.Errorf("task context validation failed: %w", err)
	}

	// 验证所有网络配置
	if configMgr, ok := ctx.GetConfigProvider().(*ConfigManager); ok {
		if err := configMgr.ValidateAllConfigs(); err != nil {
			return fmt.Errorf("config validation failed: %w", err)
		}
	}

	return nil
}

// ReloadConfigs 重新加载所有配置
func ReloadConfigs() error {
	ctx := GetTaskContext()
	if ctx == nil {
		return fmt.Errorf("task context not initialized")
	}

	if configMgr, ok := ctx.GetConfigProvider().(*ConfigManager); ok {
		if err := configMgr.ReloadAllConfigs(); err != nil {
			return fmt.Errorf("failed to reload configs: %w", err)
		}
	}

	log.Info("All configs reloaded successfully")
	return nil
}

// GetNetworkConfig 获取指定网络的配置
func GetNetworkConfig(network string) (BlockchainConfig, error) {
	ctx := GetTaskContext()
	if ctx == nil {
		return BlockchainConfig{}, fmt.Errorf("task context not initialized")
	}

	config, exists := ctx.GetConfig(network)
	if !exists {
		return BlockchainConfig{}, fmt.Errorf("config not found for network: %s", network)
	}

	return config, nil
}

// UpdateNetworkConfig 更新指定网络的配置
func UpdateNetworkConfig(network string, config BlockchainConfig) error {
	ctx := GetTaskContext()
	if ctx == nil {
		return fmt.Errorf("task context not initialized")
	}

	return ctx.UpdateConfig(network, config)
}
