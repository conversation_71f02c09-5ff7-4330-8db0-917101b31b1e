package task_v2

import (
	"fmt"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/log"
)

// ErrorHandler 错误处理器实现
type ErrorHandler struct {
	maxRetries map[ErrorType]int
	retryDelay map[ErrorType]time.Duration
}

// NewErrorHandler 创建新的错误处理器
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{
		maxRetries: map[ErrorType]int{
			ErrorTypeNetwork:       3,
			ErrorTypeRPC:           5,
			ErrorTypeDatabase:      2,
			ErrorTypeParsing:       1, // 解析错误通常不需要重试
			ErrorTypeQueue:         3,
			ErrorTypeConfiguration: 0, // 配置错误不重试
		},
		retryDelay: map[ErrorType]time.Duration{
			ErrorTypeNetwork:       time.Second * 2,
			ErrorTypeRPC:           time.Second * 1,
			ErrorTypeDatabase:      time.Second * 5,
			ErrorTypeParsing:       0,
			ErrorTypeQueue:         time.Second * 1,
			ErrorTypeConfiguration: 0,
		},
	}
}

// ReportError 报告错误
func (eh *ErrorHandler) ReportError(errorType ErrorType, taskName, network string, err error, context map[string]interface{}) bool {
	taskErr := TaskError{
		Type:     errorType,
		TaskName: taskName,
		Network:  network,
		Err:      err,
		Retries:  0,
		Context:  context,
	}

	// 记录错误
	log.Error("Task execution failed",
		"error", taskErr.Error(),
		"type", eh.getTypeString(errorType),
		"task", taskName,
		"network", network,
		"context", context)

	// 更新统计
	if network != "" {
		conf.SetBlockFail(network)
	}

	// 判断是否需要重试
	maxRetries := eh.maxRetries[errorType]
	return maxRetries > 0
}

// GetMaxRetries 获取指定错误类型的最大重试次数
func (eh *ErrorHandler) GetMaxRetries(errorType ErrorType) int {
	return eh.maxRetries[errorType]
}

// GetRetryDelay 获取指定错误类型的重试延迟
func (eh *ErrorHandler) GetRetryDelay(errorType ErrorType) time.Duration {
	return eh.retryDelay[errorType]
}

// SetMaxRetries 设置指定错误类型的最大重试次数
func (eh *ErrorHandler) SetMaxRetries(errorType ErrorType, maxRetries int) {
	eh.maxRetries[errorType] = maxRetries
}

// SetRetryDelay 设置指定错误类型的重试延迟
func (eh *ErrorHandler) SetRetryDelay(errorType ErrorType, delay time.Duration) {
	eh.retryDelay[errorType] = delay
}

// getTypeString 获取错误类型字符串
func (eh *ErrorHandler) getTypeString(errorType ErrorType) string {
	switch errorType {
	case ErrorTypeNetwork:
		return "NETWORK"
	case ErrorTypeRPC:
		return "RPC"
	case ErrorTypeDatabase:
		return "DATABASE"
	case ErrorTypeParsing:
		return "PARSING"
	case ErrorTypeQueue:
		return "QUEUE"
	case ErrorTypeConfiguration:
		return "CONFIG"
	default:
		return "UNKNOWN"
	}
}

// IsRetryableError 判断错误是否可重试
func (eh *ErrorHandler) IsRetryableError(errorType ErrorType) bool {
	return eh.maxRetries[errorType] > 0
}

// ShouldRetry 判断是否应该重试
func (eh *ErrorHandler) ShouldRetry(errorType ErrorType, currentRetries int) bool {
	return currentRetries < eh.maxRetries[errorType]
}

// CalculateBackoffDelay 计算退避延迟
func (eh *ErrorHandler) CalculateBackoffDelay(errorType ErrorType, retryCount int) time.Duration {
	baseDelay := eh.retryDelay[errorType]
	if baseDelay == 0 {
		return 0
	}

	// 指数退避，最大不超过基础延迟的8倍
	multiplier := min(1<<uint(retryCount), 8)
	return baseDelay * time.Duration(multiplier)
}

// ReportSuccess 报告成功
func (eh *ErrorHandler) ReportSuccess(taskName, network string, context map[string]interface{}) {
	log.Info("Task execution succeeded",
		"task", taskName,
		"network", network,
		"context", context)

	// 更新成功统计
	if network != "" {
		conf.SetBlockTotal(network)
	}
}

// CreateTaskError 创建任务错误
func (eh *ErrorHandler) CreateTaskError(errorType ErrorType, taskName, network string, err error, context map[string]interface{}) *TaskError {
	return &TaskError{
		Type:     errorType,
		TaskName: taskName,
		Network:  network,
		Err:      err,
		Retries:  0,
		Context:  context,
	}
}

// HandlePanic 处理panic
func (eh *ErrorHandler) HandlePanic(taskName, network string) {
	if r := recover(); r != nil {
		var err error
		switch x := r.(type) {
		case string:
			err = fmt.Errorf("panic: %s", x)
		case error:
			err = fmt.Errorf("panic: %w", x)
		default:
			err = fmt.Errorf("panic: %v", x)
		}

		eh.ReportError(ErrorTypeConfiguration, taskName, network, err, map[string]interface{}{
			"panic":     true,
			"recovered": r,
		})
	}
}

// GetErrorStats 获取错误统计信息
func (eh *ErrorHandler) GetErrorStats() map[ErrorType]map[string]interface{} {
	stats := make(map[ErrorType]map[string]interface{})

	for errorType := range eh.maxRetries {
		stats[errorType] = map[string]interface{}{
			"max_retries": eh.maxRetries[errorType],
			"retry_delay": eh.retryDelay[errorType],
			"type_name":   eh.getTypeString(errorType),
		}
	}

	return stats
}
