package task_v2

import (
	"fmt"
	"sync"

	"bepusdt/app/log"
)

// ConfigManager 配置管理器实现
type ConfigManager struct {
	blockchainConfigs map[string]BlockchainConfig
	mu                sync.RWMutex
	validators        map[string]ConfigValidator
	changeListeners   []ConfigChangeListener
}

// NewConfigManager 创建新的配置管理器
func NewConfigManager() *ConfigManager {
	cm := &ConfigManager{
		blockchainConfigs: make(map[string]BlockchainConfig),
		validators:        make(map[string]ConfigValidator),
		changeListeners:   make([]ConfigChangeListener, 0),
	}

	// 加载默认配置
	for network, config := range GetDefaultBlockchainConfigs() {
		cm.blockchainConfigs[network] = config
	}

	// 注册默认验证器
	cm.RegisterValidator("default", func(config BlockchainConfig) error {
		return config.Validate()
	})

	return cm
}

// GetConfig 获取指定网络的配置
func (cm *ConfigManager) GetConfig(network string) (BlockchainConfig, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	config, exists := cm.blockchainConfigs[network]
	return config, exists
}

// UpdateConfig 更新指定网络的配置
func (cm *ConfigManager) UpdateConfig(network string, config BlockchainConfig) error {
	// 验证配置
	for name, validator := range cm.validators {
		if err := validator(config); err != nil {
			return fmt.Errorf("validation failed (%s): %w", name, err)
		}
	}

	cm.mu.Lock()
	oldConfig := cm.blockchainConfigs[network]
	cm.blockchainConfigs[network] = config
	cm.mu.Unlock()

	// 通知监听器
	for _, listener := range cm.changeListeners {
		go listener(network, oldConfig, config)
	}

	log.Info("Blockchain config updated", "network", network)
	return nil
}

// RegisterValidator 注册配置验证器
func (cm *ConfigManager) RegisterValidator(name string, validator ConfigValidator) {
	cm.validators[name] = validator
}

// RegisterChangeListener 注册配置变更监听器
func (cm *ConfigManager) RegisterChangeListener(listener ConfigChangeListener) {
	cm.changeListeners = append(cm.changeListeners, listener)
}

// GetAllConfigs 获取所有配置
func (cm *ConfigManager) GetAllConfigs() map[string]BlockchainConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	configs := make(map[string]BlockchainConfig)
	for k, v := range cm.blockchainConfigs {
		configs[k] = v
	}
	return configs
}

// ValidateAllConfigs 验证所有配置
func (cm *ConfigManager) ValidateAllConfigs() error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	for network, config := range cm.blockchainConfigs {
		for name, validator := range cm.validators {
			if err := validator(config); err != nil {
				return fmt.Errorf("validation failed for network %s (%s): %w", network, name, err)
			}
		}
	}
	return nil
}

// ReloadConfig 重新加载指定网络的配置
func (cm *ConfigManager) ReloadConfig(network string) error {
	defaultConfigs := GetDefaultBlockchainConfigs()
	if config, exists := defaultConfigs[network]; exists {
		return cm.UpdateConfig(network, config)
	}
	return fmt.Errorf("network %s not found in default configs", network)
}

// ReloadAllConfigs 重新加载所有配置
func (cm *ConfigManager) ReloadAllConfigs() error {
	defaultConfigs := GetDefaultBlockchainConfigs()
	for network, config := range defaultConfigs {
		if err := cm.UpdateConfig(network, config); err != nil {
			return fmt.Errorf("failed to reload config for network %s: %w", network, err)
		}
	}
	return nil
}

// GetConfigCount 获取配置数量
func (cm *ConfigManager) GetConfigCount() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return len(cm.blockchainConfigs)
}

// HasConfig 检查是否存在指定网络的配置
func (cm *ConfigManager) HasConfig(network string) bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	_, exists := cm.blockchainConfigs[network]
	return exists
}

// RemoveConfig 移除指定网络的配置
func (cm *ConfigManager) RemoveConfig(network string) bool {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.blockchainConfigs[network]; exists {
		delete(cm.blockchainConfigs, network)
		log.Info("Blockchain config removed", "network", network)
		return true
	}
	return false
}
