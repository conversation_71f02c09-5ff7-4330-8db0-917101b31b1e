package task_v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"bepusdt/app/log"
)

// CompatibilityLayer 兼容性层
type CompatibilityLayer struct {
	newSystemEnabled bool
	oldSystemActive  bool
	mu               sync.RWMutex
	snapshot         *SystemSnapshot
}

// SystemSnapshot 系统快照
type SystemSnapshot struct {
	Configs   map[string]BlockchainConfig
	Tasks     []task
	Timestamp time.Time
}

var compatibility = &CompatibilityLayer{
	newSystemEnabled: false,
	oldSystemActive:  true,
}

// EnableNewSystem 启用新系统
func EnableNewSystem() error {
	compatibility.mu.Lock()
	defer compatibility.mu.Unlock()

	if compatibility.newSystemEnabled {
		return fmt.Errorf("new system already enabled")
	}

	// 创建系统快照
	snapshot, err := createSystemSnapshot()
	if err != nil {
		return fmt.Errorf("failed to create system snapshot: %w", err)
	}

	compatibility.snapshot = snapshot
	compatibility.newSystemEnabled = true
	compatibility.oldSystemActive = false

	log.Info("New task system enabled")
	return nil
}

// DisableNewSystem 禁用新系统，回退到旧系统
func DisableNewSystem() error {
	compatibility.mu.Lock()
	defer compatibility.mu.Unlock()

	if !compatibility.newSystemEnabled {
		return fmt.Errorf("new system not enabled")
	}

	// 恢复系统快照
	if compatibility.snapshot != nil {
		if err := restoreSystemSnapshot(compatibility.snapshot); err != nil {
			log.Error("Failed to restore system snapshot", "error", err.Error())
			// 即使恢复失败，也要切换回旧系统
		}
	}

	compatibility.newSystemEnabled = false
	compatibility.oldSystemActive = true

	log.Info("Fallback to old task system")
	return nil
}

// IsNewSystemEnabled 检查新系统是否启用
func IsNewSystemEnabled() bool {
	compatibility.mu.RLock()
	defer compatibility.mu.RUnlock()
	return compatibility.newSystemEnabled
}

// IsOldSystemActive 检查旧系统是否激活
func IsOldSystemActive() bool {
	compatibility.mu.RLock()
	defer compatibility.mu.RUnlock()
	return compatibility.oldSystemActive
}

// createSystemSnapshot 创建系统快照
func createSystemSnapshot() (*SystemSnapshot, error) {
	snapshot := &SystemSnapshot{
		Configs:   make(map[string]BlockchainConfig),
		Timestamp: time.Now(),
	}

	// 保存当前配置
	if IsInitialized() {
		ctx := GetTaskContext()
		if ctx != nil {
			if configMgr, ok := ctx.GetConfigProvider().(*ConfigManager); ok {
				snapshot.Configs = configMgr.GetAllConfigs()
			}
		}
	}

	// 保存当前任务状态
	mu.Lock()
	snapshot.Tasks = make([]task, len(tasks))
	copy(snapshot.Tasks, tasks)
	mu.Unlock()

	log.Info("System snapshot created", "config_count", len(snapshot.Configs), "task_count", len(snapshot.Tasks))
	return snapshot, nil
}

// restoreSystemSnapshot 恢复系统快照
func restoreSystemSnapshot(snapshot *SystemSnapshot) error {
	if snapshot == nil {
		return fmt.Errorf("snapshot is nil")
	}

	// 恢复配置
	if IsInitialized() {
		ctx := GetTaskContext()
		if ctx != nil {
			for network, config := range snapshot.Configs {
				if err := ctx.UpdateConfig(network, config); err != nil {
					log.Error("Failed to restore config", "network", network, "error", err.Error())
				}
			}
		}
	}

	// 恢复任务状态
	mu.Lock()
	tasks = make([]task, len(snapshot.Tasks))
	copy(tasks, snapshot.Tasks)
	mu.Unlock()

	log.Info("System snapshot restored", "timestamp", snapshot.Timestamp)
	return nil
}

// CompatibleInit 兼容性初始化
func CompatibleInit() error {
	if IsNewSystemEnabled() {
		return Init() // 使用新系统初始化
	}
	return oldSystemInit() // 使用旧系统初始化
}

// oldSystemInit 旧系统初始化（占位符）
func oldSystemInit() error {
	// 这里应该调用原有的初始化逻辑
	// 由于我们是在新目录中，这里只是记录日志
	log.Info("Old system initialization called")
	return nil
}

// CompatibleStart 兼容性启动
func CompatibleStart(ctx context.Context) {
	if IsNewSystemEnabled() {
		Start(ctx) // 使用新系统启动
	} else {
		oldSystemStart(ctx) // 使用旧系统启动
	}
}

// oldSystemStart 旧系统启动（占位符）
func oldSystemStart(ctx context.Context) {
	// 这里应该调用原有的启动逻辑
	log.Info("Old system start called")
}

// MigrationStatus 迁移状态
type MigrationStatus struct {
	NewSystemEnabled bool      `json:"new_system_enabled"`
	OldSystemActive  bool      `json:"old_system_active"`
	SnapshotTime     time.Time `json:"snapshot_time"`
	TaskCount        int       `json:"task_count"`
	ConfigCount      int       `json:"config_count"`
}

// GetMigrationStatus 获取迁移状态
func GetMigrationStatus() MigrationStatus {
	compatibility.mu.RLock()
	defer compatibility.mu.RUnlock()

	status := MigrationStatus{
		NewSystemEnabled: compatibility.newSystemEnabled,
		OldSystemActive:  compatibility.oldSystemActive,
		TaskCount:        GetTaskCount(),
	}

	if compatibility.snapshot != nil {
		status.SnapshotTime = compatibility.snapshot.Timestamp
		status.ConfigCount = len(compatibility.snapshot.Configs)
	}

	return status
}

// ValidateMigration 验证迁移状态
func ValidateMigration() error {
	if !IsNewSystemEnabled() {
		return fmt.Errorf("new system not enabled")
	}

	if !IsInitialized() {
		return fmt.Errorf("new system not initialized")
	}

	// 验证任务系统
	if err := ValidateSystem(); err != nil {
		return fmt.Errorf("system validation failed: %w", err)
	}

	return nil
}

// PerformGradualMigration 执行渐进式迁移
func PerformGradualMigration() error {
	log.Info("Starting gradual migration to new system")

	// 步骤1：初始化新系统
	if err := Init(); err != nil {
		return fmt.Errorf("failed to initialize new system: %w", err)
	}

	// 步骤2：验证新系统
	if err := ValidateSystem(); err != nil {
		return fmt.Errorf("new system validation failed: %w", err)
	}

	// 步骤3：创建快照
	snapshot, err := createSystemSnapshot()
	if err != nil {
		return fmt.Errorf("failed to create snapshot: %w", err)
	}

	// 步骤4：启用新系统
	compatibility.mu.Lock()
	compatibility.snapshot = snapshot
	compatibility.newSystemEnabled = true
	compatibility.oldSystemActive = false
	compatibility.mu.Unlock()

	log.Info("Gradual migration completed successfully")
	return nil
}

// RollbackMigration 回滚迁移
func RollbackMigration() error {
	log.Info("Starting migration rollback")

	compatibility.mu.Lock()
	defer compatibility.mu.Unlock()

	if !compatibility.newSystemEnabled {
		return fmt.Errorf("new system not enabled, nothing to rollback")
	}

	// 恢复快照
	if compatibility.snapshot != nil {
		if err := restoreSystemSnapshot(compatibility.snapshot); err != nil {
			log.Error("Failed to restore snapshot during rollback", "error", err.Error())
		}
	}

	// 切换回旧系统
	compatibility.newSystemEnabled = false
	compatibility.oldSystemActive = true

	log.Info("Migration rollback completed")
	return nil
}

// MonitorMigration 监控迁移状态
func MonitorMigration(ctx context.Context) {
	ticker := time.NewTicker(time.Minute * 5)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if IsNewSystemEnabled() {
				if err := ValidateMigration(); err != nil {
					log.Error("Migration validation failed", "error", err.Error())
					
					// 自动回滚
					if err := RollbackMigration(); err != nil {
						log.Error("Auto rollback failed", "error", err.Error())
					}
				}
			}
		}
	}
}

// GetCompatibilityInfo 获取兼容性信息
func GetCompatibilityInfo() map[string]interface{} {
	status := GetMigrationStatus()
	
	return map[string]interface{}{
		"migration_status": status,
		"system_info": map[string]interface{}{
			"new_system_available": true,
			"old_system_available": true,
			"current_system":       getCurrentSystemName(),
		},
		"capabilities": map[string]interface{}{
			"can_migrate":  !status.NewSystemEnabled,
			"can_rollback": status.NewSystemEnabled,
			"auto_monitor": IsNewSystemEnabled(),
		},
	}
}

// getCurrentSystemName 获取当前系统名称
func getCurrentSystemName() string {
	if IsNewSystemEnabled() {
		return "task_v2"
	}
	return "task_v1"
}

// TestMigration 测试迁移功能
func TestMigration() error {
	log.Info("Testing migration functionality")

	// 测试快照创建
	snapshot, err := createSystemSnapshot()
	if err != nil {
		return fmt.Errorf("snapshot creation test failed: %w", err)
	}

	// 测试快照恢复
	if err := restoreSystemSnapshot(snapshot); err != nil {
		return fmt.Errorf("snapshot restoration test failed: %w", err)
	}

	log.Info("Migration functionality test passed")
	return nil
}

// CleanupMigration 清理迁移相关资源
func CleanupMigration() error {
	compatibility.mu.Lock()
	defer compatibility.mu.Unlock()

	compatibility.snapshot = nil
	log.Info("Migration resources cleaned up")
	return nil
}
