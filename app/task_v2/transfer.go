package task_v2

import (
	"context"
	"fmt"
	"time"

	"bepusdt/app/conf"
	"bepusdt/app/model"

	"github.com/shopspring/decimal"
)

// initTransferTasks 初始化转账处理任务
func initTransferTasks(ctx *TaskContext) error {
	// 注册转账处理任务
	registerTask(task{callback: func(taskCtx context.Context) {
		orderTransferHandle(ctx, taskCtx)
	}})

	// 注册资源处理任务（Tron网络）
	registerTask(task{callback: func(taskCtx context.Context) {
		tronResourceHandle(ctx, taskCtx)
	}})

	ctx.LogInfo("Transfer tasks initialized")
	return nil
}

// orderTransferHandle 订单转账处理
func orderTransferHandle(ctx *TaskContext, taskCtx context.Context) {
	transferQueue := ctx.GetQueueProvider().GetTransferQueue()

	for {
		select {
		case <-taskCtx.Done():
			return
		case transfers := <-transferQueue.Receive():
			if len(transfers) == 0 {
				continue
			}

			ctx.LogInfo("Processing transfers", "count", len(transfers))

			// 获取等待支付的订单
			orders := getAllWaitingOrders()
			if len(orders) == 0 {
				// 没有等待支付的订单，将转账发送到非订单队列
				if err := ctx.SendToNotOrderQueue(transfers); err != nil {
					ctx.ReportError(ErrorTypeQueue, "orderTransferHandle", "", err, map[string]interface{}{
						"transfer_count": len(transfers),
						"action":         "send_to_not_order_queue",
					})
				}
				continue
			}

			var matchedTransfers []Transfer
			var unmatchedTransfers []Transfer

			// 处理每个转账
			for _, transfer := range transfers {
				if order := matchTransferToOrder(transfer, orders); order != nil {
					// 匹配到订单，标记为确认中
					order.MarkConfirming(transfer.BlockNum, transfer.FromAddress, transfer.TxHash, transfer.Timestamp)
					matchedTransfers = append(matchedTransfers, transfer)

					ctx.LogInfo("Transfer matched to order",
						"tx_hash", transfer.TxHash,
						"order_id", order.OrderId,
						"amount", transfer.Amount.String(),
						"address", transfer.RecvAddress)
				} else {
					unmatchedTransfers = append(unmatchedTransfers, transfer)
				}
			}

			// 将未匹配的转账发送到非订单队列
			if len(unmatchedTransfers) > 0 {
				if err := ctx.SendToNotOrderQueue(unmatchedTransfers); err != nil {
					ctx.ReportError(ErrorTypeQueue, "orderTransferHandle", "", err, map[string]interface{}{
						"unmatched_count": len(unmatchedTransfers),
					})
				}
			}

			ctx.ReportSuccess("orderTransferHandle", "", map[string]interface{}{
				"total_transfers":     len(transfers),
				"matched_transfers":   len(matchedTransfers),
				"unmatched_transfers": len(unmatchedTransfers),
			})
		}
	}
}

// getAllWaitingOrders 获取所有等待支付的订单
func getAllWaitingOrders() map[string]*model.TradeOrders {
	var orders []model.TradeOrders
	model.DB.Where("status = ?", model.OrderStatusWaiting).Find(&orders)

	orderMap := make(map[string]*model.TradeOrders)
	now := time.Now()

	for i := range orders {
		order := &orders[i]

		// 检查订单是否过期
		if now.After(order.ExpiredAt) {
			order.SetExpired()
			continue
		}

		// 构建匹配键：地址+金额+交易类型
		key := fmt.Sprintf("%s%s%s", order.Address, order.Amount, order.TradeType)
		orderMap[key] = order
	}

	return orderMap
}

// matchTransferToOrder 将转账匹配到订单
func matchTransferToOrder(transfer Transfer, orders map[string]*model.TradeOrders) *model.TradeOrders {
	// 构建匹配键
	key := fmt.Sprintf("%s%s%s", transfer.RecvAddress, transfer.Amount.String(), transfer.TradeType)

	order, exists := orders[key]
	if !exists {
		return nil
	}

	// 验证时间有效性
	if !order.CreatedAt.Before(transfer.Timestamp) || !order.ExpiredAt.After(transfer.Timestamp) {
		return nil
	}

	return order
}

// tronResourceHandle Tron资源处理
func tronResourceHandle(ctx *TaskContext, taskCtx context.Context) {
	resourceQueue := ctx.GetQueueProvider().GetResourceQueue()

	for {
		select {
		case <-taskCtx.Done():
			return
		case resources := <-resourceQueue.Receive():
			if len(resources) == 0 {
				continue
			}

			ctx.LogInfo("Processing Tron resources", "count", len(resources))

			for _, resource := range resources {
				if err := processTronResource(ctx, resource); err != nil {
					ctx.ReportError(ErrorTypeDatabase, "tronResourceHandle", "tron", err, map[string]interface{}{
						"resource_id":   resource.ID,
						"resource_type": resource.Type,
						"from_address":  resource.FromAddress,
						"recv_address":  resource.RecvAddress,
					})
				}
			}

			ctx.ReportSuccess("tronResourceHandle", "tron", map[string]interface{}{
				"processed_count": len(resources),
			})
		}
	}
}

// processTronResource 处理单个Tron资源
func processTronResource(ctx *TaskContext, resource Resource) error {
	// 检查是否需要通知
	if !model.IsNeedNotifyByTxid(resource.ID) {
		return nil
	}

	// 查找对应的钱包地址
	var walletAddress model.WalletAddress
	err := model.DB.Where("address = ? AND trade_type = ?", resource.RecvAddress, model.OrderTradeTypeTronTrx).First(&walletAddress).Error
	if err != nil {
		return fmt.Errorf("wallet address not found: %w", err)
	}

	// 检查是否启用了其他通知
	if walletAddress.OtherNotify != model.OtherNotifyEnable {
		return nil
	}

	// 创建通知记录
	notifyRecord := model.NotifyRecord{
		Txid: resource.ID,
	}

	if err := model.DB.Create(&notifyRecord).Error; err != nil {
		return fmt.Errorf("failed to create notify record: %w", err)
	}

	ctx.LogInfo("Tron resource processed",
		"resource_id", resource.ID,
		"from_address", resource.FromAddress,
		"recv_address", resource.RecvAddress,
		"balance", resource.Balance)

	return nil
}

// inAmountRange 检查金额是否在允许范围内
func inAmountRange(payAmount decimal.Decimal) bool {
	maxAmount := conf.GetPaymentAmountMax()
	minAmount := conf.GetPaymentAmountMin()

	if payAmount.GreaterThan(maxAmount) {
		return false
	}

	if payAmount.LessThan(minAmount) {
		return false
	}

	return true
}

// convertLegacyTransfer 将旧版本的 transfer 结构转换为新版本的 Transfer
func convertLegacyTransfer(legacy interface{}) Transfer {
	// 这里需要根据实际的旧版本结构进行转换
	// 暂时返回空结构，实际使用时需要实现具体的转换逻辑
	return Transfer{}
}

// validateTransfer 验证转账数据的有效性
func validateTransfer(transfer Transfer) error {
	if transfer.TxHash == "" {
		return fmt.Errorf("transaction hash cannot be empty")
	}

	if transfer.RecvAddress == "" {
		return fmt.Errorf("receive address cannot be empty")
	}

	if transfer.Amount.IsZero() || transfer.Amount.IsNegative() {
		return fmt.Errorf("amount must be positive")
	}

	if transfer.TradeType == "" {
		return fmt.Errorf("trade type cannot be empty")
	}

	if transfer.Network == "" {
		return fmt.Errorf("network cannot be empty")
	}

	return nil
}

// getTransferKey 获取转账的唯一键
func getTransferKey(transfer Transfer) string {
	return fmt.Sprintf("%s:%s", transfer.Network, transfer.TxHash)
}

// isTransferProcessed 检查转账是否已经处理过
func isTransferProcessed(transfer Transfer) bool {
	var count int64
	model.DB.Model(&model.TradeOrders{}).Where("trade_hash = ?", transfer.TxHash).Count(&count)
	return count > 0
}

// getTransfersByNetwork 按网络分组转账
func getTransfersByNetwork(transfers []Transfer) map[string][]Transfer {
	networkTransfers := make(map[string][]Transfer)

	for _, transfer := range transfers {
		networkTransfers[transfer.Network] = append(networkTransfers[transfer.Network], transfer)
	}

	return networkTransfers
}
