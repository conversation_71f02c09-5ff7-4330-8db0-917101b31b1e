package task_v2

import (
	"maps"
	"time"

	"bepusdt/app/conf"
)

// 预定义的区块链网络配置
var blockchainConfigs = map[string]BlockchainConfig{
	conf.Ethereum: {
		Network:          conf.Ethereum,
		Endpoint:         conf.GetEthereumRpcEndpoint(),
		InitOffset:       -100,
		ConfirmOffset:    12,
		DelayOffset:      0,
		RollInterval:     time.Second * 12,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       3,
		RetryInterval:    time.Second * 2,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
	conf.Bsc: {
		Network:          conf.Bsc,
		Endpoint:         conf.GetBscRpcEndpoint(),
		InitOffset:       -400,
		ConfirmOffset:    15,
		DelayOffset:      0,
		RollInterval:     time.Second * 5,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       3,
		RetryInterval:    time.Second * 2,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
	conf.Polygon: {
		Network:          conf.Polygon,
		Endpoint:         conf.GetPolygonRpcEndpoint(),
		InitOffset:       -600,
		ConfirmOffset:    40,
		DelayOffset:      0,
		RollInterval:     time.Second * 5,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       3,
		RetryInterval:    time.Second * 2,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
	conf.Arbitrum: {
		Network:          conf.Arbitrum,
		Endpoint:         conf.GetArbitrumRpcEndpoint(),
		InitOffset:       -600,
		ConfirmOffset:    40,
		DelayOffset:      0,
		RollInterval:     time.Second * 5,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       3,
		RetryInterval:    time.Second * 2,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
	conf.Xlayer: {
		Network:          conf.Xlayer,
		Endpoint:         conf.GetXlayerRpcEndpoint(),
		InitOffset:       -600,
		ConfirmOffset:    12,
		DelayOffset:      3, // 特殊处理：Xlayer需要延迟
		RollInterval:     time.Second * 3,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       5, // Xlayer不稳定，增加重试次数
		RetryInterval:    time.Second * 3,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
	conf.Base: {
		Network:          conf.Base,
		Endpoint:         conf.GetBaseRpcEndpoint(),
		InitOffset:       -600,
		ConfirmOffset:    40,
		DelayOffset:      0,
		RollInterval:     time.Second * 5,
		ConfirmInterval:  time.Second * 5,
		MaxRetries:       3,
		RetryInterval:    time.Second * 2,
		HealthCheck:      true,
		FallbackEndpoint: "",
	},
}

// GetDefaultBlockchainConfigs 获取默认区块链配置
func GetDefaultBlockchainConfigs() map[string]BlockchainConfig {
	// 返回配置的副本，避免外部修改
	configs := make(map[string]BlockchainConfig)
	maps.Copy(configs, blockchainConfigs)
	return configs
}

// GetSupportedNetworks 获取支持的网络列表
func GetSupportedNetworks() []string {
	networks := make([]string, 0, len(blockchainConfigs))
	for network := range blockchainConfigs {
		networks = append(networks, network)
	}
	return networks
}

// IsNetworkSupported 检查网络是否支持
func IsNetworkSupported(network string) bool {
	_, exists := blockchainConfigs[network]
	return exists
}
