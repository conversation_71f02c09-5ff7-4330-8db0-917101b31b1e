package task

import (
	"context"
	"time"

	"bepusdt/app/conf"

	"github.com/smallnest/chanx"
)

func arbitrumInit() {
	ctx := context.Background()
	arb := evm{
		Network:  conf.Arbitrum,
		Endpoint: conf.GetArbitrumRpcEndpoint(),
		Block: block{
			InitStartOffset: -600,
			ConfirmedOffset: 40,
		},
		blockScanQueue: chanx.NewUnboundedChan[evmBlock](ctx, 30),
	}

	register(task{callback: arb.blockDispatch})
	register(task{callback: arb.blockRoll, duration: time.Second * 5})
	register(task{callback: arb.tradeConfirmHandle, duration: time.Second * 5})
}
