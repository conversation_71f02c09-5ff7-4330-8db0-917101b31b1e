package rate

import (
	"bepusdt/app/conf"
	"bepusdt/app/help"
	"bepusdt/app/log"
	"math"
	"regexp"

	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

var okxTrxCnyCalcRate = 0.0
var okxUsdtCnyCalcRate = 0.0
var okxUsdcCnyCalcRate = 0.0
var okxUsdtCnyRawRate = conf.DefaultUsdtCnyRate // okx 交易所 usdt/cny 原始汇率；6.4是初始默认值值，后续但凡有新的汇率数据更新都会覆盖这个值
var okxUsdcCnyRawRate = conf.DefaultUsdcCnyRate // okx 交易所 usdc/cny 原始汇率；6.4是初始默认值值，后续但凡有新的汇率数据更新都会覆盖这个值
var okxTrxCnyRawRate = conf.DefaultTrxCnyRate   // okx 交易所 trx/cny 原始汇率
var okxRatePrecision = 2                        // 汇率保留位数，强迫症，另一方面两位小数足以覆盖大部分CNY使用场景

func GetTrxCalcRate() float64 {

	return okxTrxCnyCalcRate
}

func GetUsdtCalcRate() float64 {

	return okxUsdtCnyCalcRate
}

func GetUsdcCalcRate() float64 {

	return okxUsdcCnyCalcRate
}

func GetOkxUsdtRawRate() float64 {

	return okxUsdtCnyRawRate
}

func GetOkxUsdcRawRate() float64 {

	return okxUsdcCnyRawRate
}

func GetOkxTrxRawRate() float64 {

	return okxTrxCnyRawRate
}

func SetOkxTrxCnyRate(syntax string, rawRate float64) {
	rawRate = round(rawRate, okxRatePrecision)
	okxTrxCnyRawRate = rawRate
	okxTrxCnyCalcRate = ParseFloatRate(syntax, rawRate)
}

func SetOkxUsdtCnyRate(syntax string, rawRate float64) {
	rawRate = round(rawRate, okxRatePrecision)
	okxUsdtCnyRawRate = rawRate
	okxUsdtCnyCalcRate = ParseFloatRate(syntax, rawRate)
}

func SetOkxUsdcCnyRate(syntax string, rawRate float64) {
	rawRate = round(rawRate, okxRatePrecision)
	okxUsdcCnyRawRate = rawRate
	okxUsdcCnyCalcRate = ParseFloatRate(syntax, rawRate)
}

func ParseFloatRate(syntax string, rawVal float64) float64 {
	if syntax == "" {

		return rawVal
	}

	if help.IsNumber(syntax) {

		return cast.ToFloat64(syntax)
	}

	match, err := regexp.MatchString(`^[~+-]\d+(\.\d+)?$`, syntax)
	if !match || err != nil {
		log.Error("浮动语法解析错误", err)

		return 0
	}

	var act = syntax[0:1]
	var raw = decimal.NewFromFloat(rawVal)
	var base = decimal.NewFromFloat(cast.ToFloat64(syntax[1:]))
	var result float64 = 0

	switch act {
	case "~":
		result = raw.Mul(base).InexactFloat64()
	case "+":
		result = raw.Add(base).InexactFloat64()
	case "-":
		result = raw.Sub(base).InexactFloat64()
	}

	return round(result, okxRatePrecision)
}

func round(val float64, precision int) float64 {
	// Round 四舍五入，ROUND_HALF_UP 模式实现
	// 返回将 val 根据指定精度 precision（十进制小数点后数字的数目）进行四舍五入的结果。precision 也可以是负数或零。

	if precision == 0 {
		return math.Round(val)
	}

	p := math.Pow10(precision)
	if precision < 0 {
		return math.Floor(val*p+0.5) * math.Pow10(-precision)
	}

	return math.Floor(val*p+0.5) / p
}
