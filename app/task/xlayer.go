package task

import (
	"context"
	"time"

	"bepusdt/app/conf"

	"github.com/smallnest/chanx"
)

func xlayerInit() {
	ctx := context.Background()
	xlayer := evm{
		Network:  conf.Xlayer,
		Endpoint: conf.GetXlayerRpcEndpoint(),
		Block: block{
			InitStartOffset: -600,
			RollDelayOffset: 3,
			ConfirmedOffset: 12,
		},
		blockScanQueue: chanx.NewUnboundedChan[evmBlock](ctx, 30),
	}

	register(task{callback: xlayer.blockDispatch})
	register(task{callback: xlayer.blockRoll, duration: time.Second * 3})
	register(task{callback: xlayer.tradeConfirmHandle, duration: time.Second * 5})
}
