## BEpusdt 项目 `/app/task/` 目录深度分析报告

### 1. 架构分析

#### 1.1 目录职责和作用

`/app/task/` 目录是整个 BEpusdt 项目的**核心任务调度系统**，负责：

- **区块链监控**：监控多个区块链网络的区块和交易
- **支付处理**：处理 USDT/USDC/TRX 等数字货币的支付确认
- **通知系统**：处理支付成功后的回调通知和重试机制
- **汇率管理**：从 OKX 交易所获取实时汇率数据
- **资源管理**：处理 Tron 网络的能量资源委托

#### 1.2 与其他模块的依赖关系

```go path=main/main.go mode=EXCERPT
var initializers = []Initializer{conf.Init, log.Init, bot.Init, model.Init, task.Init}

func main() {
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel()

    task.Start(ctx)
    web.Start(ctx)
```

**依赖关系图**：

- **上游依赖**：`app/conf`（配置）、`app/log`（日志）、`app/model`（数据模型）
- **下游服务**：`app/web/notify`（通知服务）、`app/bot`（Telegram 机器人）
- **外部依赖**：区块链 RPC 节点、OKX API、数据库

#### 1.3 文件功能分工

| 文件                          | 职责            | 核心功能                     |
| ----------------------------- | --------------- | ---------------------------- |
| `task.go`                     | 任务管理核心    | 任务注册、启动、调度         |
| `evm.go`                      | EVM 链通用处理  | 区块扫描、交易解析、确认逻辑 |
| `eth.go/bsc.go/polygon.go` 等 | 具体链初始化    | 各链特定参数配置             |
| `tron.go`                     | Tron 网络处理   | TRC20 代币监控、资源管理     |
| `solana.go`                   | Solana 网络处理 | SPL 代币监控                 |
| `transfer.go`                 | 转账处理        | 订单匹配、支付确认           |
| `notify.go`                   | 通知管理        | 回调重试、状态通知           |
| `webhook.go`                  | Webhook 处理    | 外部系统集成                 |
| `okx.go`                      | 汇率获取        | 实时汇率监控                 |
| `rate/rate.go`                | 汇率计算        | 汇率解析和计算               |

### 2. 运行流程分析

#### 2.1 任务系统启动流程

```go path=app/task/task.go mode=EXCERPT
func Init() error {
    bscInit()
    ethInit()
    polygonInit()
    arbitrumInit()
    xlayerInit()
    baseInit()
    return nil
}

func Start(ctx context.Context) {
    mu.Lock()
    defer mu.Unlock()

    for _, t := range tasks {
        go func(t task) {
            if t.duration <= 0 {
                t.callback(ctx)
                return
            }
            t.callback(ctx)
            ticker := time.NewTicker(t.duration)
            defer ticker.Stop()
            for {
                select {
                case <-ctx.Done():
                    return
                case <-ticker.C:
                    t.callback(ctx)
                }
            }
        }(t)
    }
}
```

**启动流程**：

1. **初始化阶段**：各区块链网络初始化，注册任务到全局任务列表
2. **启动阶段**：为每个任务创建独立的 goroutine
3. **调度阶段**：根据任务的 duration 进行定时调度

#### 2.2 任务生命周期管理

**任务类型分类**：

- **一次性任务**：`duration = 0`，如 `blockDispatch`（区块分发）
- **定时任务**：`duration > 0`，如 `blockRoll`（区块轮询）

**典型任务生命周期**：

1. **区块轮询** → **区块分发** → **交易解析** → **订单匹配** → **支付确认** → **通知发送**

#### 2.3 区块链监控流程

```go path=app/task/evm.go mode=EXCERPT
func (e *evm) blockRoll(ctx context.Context) {
    // 获取最新区块高度
    post := []byte(`{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`)
    // ...处理响应
    var now = help.HexStr2Int(res.Get("result").String()).Int64() - e.Block.RollDelayOffset

    // 区块高度变化检测和分批处理
    for from := lastBlockNumber + 1; from <= now; from += blockParseMaxNum {
        to := from + blockParseMaxNum - 1
        if to > now {
            to = now
        }
        e.blockScanQueue.In <- evmBlock{From: from, To: to}
    }
}
```
